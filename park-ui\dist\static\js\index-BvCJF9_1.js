import{_ as Ne,M as Pe,g as Se,r as m,A as Te,R as xe,d as i,O as X,c as B,o as s,P as w,e,Q as Ce,k as n,f as a,l as Ie,J as Y,K as Z,i as f,q as d,N as ee,t as U,h as C,S as ze,j as Ue,B as ae,y as We,z as De}from"./index-kDFdMZgN.js";import{l as Re,a as le,b as $e,d as qe,u as Oe,c as Be}from"./package-CrULT0ad.js";import{C as te}from"./index-D3uM1NSj.js";const Ee=I=>(We("data-v-d64d8d34"),I=I(),De(),I),je={class:"app-container"},Fe=Ee(()=>C("i",{class:"el-icon-box",style:{"margin-right":"4px"}},null,-1)),Qe={style:{display:"flex","align-items":"center","justify-content":"center"}},Ke={class:"price-text"},Ae={class:"dialog-footer"},Le=Pe({name:"VipPackage"}),Me=Object.assign(Le,{components:{CustomPagination:te}},{setup(I){const{proxy:h}=Se(),{vip_package_type:W}=h.useDict("vip_package_type"),E=m([]),y=m(!1),D=m(!0),z=m(!0),R=m([]),j=m(!0),F=m(!0),Q=m(0),$=m(""),K=m([]),S=m([]),ne=m(),oe=Te({form:{},queryParams:{pageNum:1,pageSize:10,packageName:null,packageType:null,warehouseId:null},rules:{packageName:[{required:!0,message:"套餐名称不能为空",trigger:"blur"}],packageType:[{required:!0,message:"套餐类型不能为空",trigger:"change"}],packagePrice:[{required:!0,message:"套餐价格不能为空",trigger:"blur"}]}}),{queryParams:p,form:u,rules:re}=xe(oe);function V(){D.value=!0,Re(p.value).then(o=>{E.value=o.rows,Q.value=o.total,D.value=!1})}function A(){le().then(o=>{K.value=o.data,S.value=L(o.data)})}function L(o){if(!o||o.length===0)return[];const t=o.filter(r=>T(r)),c=o.filter(r=>!T(r));return t.map(r=>{const g=c.filter(_=>_.parentId===r.id).map(_=>({value:_.id,label:_.warehouseName,leaf:!0}));return{value:r.id,label:r.warehouseName,children:g.length>0?g:void 0}})}function ue(o,t){if(!o||!t||t.length===0)return null;const c=String(o),r=t.find(_=>String(_.id)===c);if(!r)return null;if(T(r))return[r.id];const g=t.find(_=>String(_.id)===String(r.parentId));return g?[g.id,r.id]:[r.id]}function T(o){const t=o.parentId;return t===0||t==="0"||t===null||t===void 0}function ie(o){return T(o)?"primary":"success"}function se(o){return T(o)?"el-icon-office-building":"el-icon-location"}function pe(o,t){return o?o.length<=t?o:o.substring(0,t)+"...":""}function de(){y.value=!1,q()}function q(){u.value={id:null,warehouseId:null,packageName:null,packageType:null,packagePrice:null,remark:null},h.resetForm("packageRef")}function O(){p.value.pageNum=1,V()}function ce(){h.resetForm("queryRef"),Object.assign(p.value,{pageNum:1,pageSize:10,packageName:null,packageType:null,warehouseId:null}),O()}function fe(o){R.value=o.map(t=>t.id),j.value=o.length!=1,F.value=!o.length}function ge(){q(),S.value.length===0&&A(),y.value=!0,$.value="添加会员套餐配置"}function M(o){q();const t=o.id||R.value;Promise.all([le(),$e(t)]).then(([c,r])=>{K.value=c.data,S.value=L(c.data),y.value=!0,$.value="修改会员套餐配置",ae(()=>{u.value=r.data,ae(()=>{if(u.value.warehouseId){const g=ue(u.value.warehouseId,c.data);g&&g.length>0&&(u.value.warehouseId=g)}})})}).catch(c=>{console.error("加载数据失败:",c),h.$modal.msgError("加载数据失败")})}function me(){h.$refs.packageRef.validate(o=>{o&&(u.value.id!=null?Oe(u.value).then(t=>{h.$modal.msgSuccess("修改成功"),y.value=!1,V()}):Be(u.value).then(t=>{h.$modal.msgSuccess("新增成功"),y.value=!1,V()}))})}function J(o){const t=o.id||R.value;h.$modal.confirm('是否确认删除会员套餐配置编号为"'+t+'"的数据项？').then(function(){return qe(t)}).then(()=>{V(),h.$modal.msgSuccess("删除成功")}).catch(()=>{})}function _e(){h.download("system/vip/package/export",{...p.value},`vip_package_${new Date().getTime()}.xlsx`)}return V(),A(),(o,t)=>{const c=i("el-input"),r=i("el-form-item"),g=i("el-option"),_=i("el-select"),G=i("el-cascader"),k=i("el-button"),H=i("el-form"),v=i("el-col"),he=i("right-toolbar"),b=i("el-row"),N=i("el-table-column"),x=i("el-tag"),ke=i("dict-tag"),ve=i("el-table"),ye=i("el-input-number"),be=i("el-dialog"),P=X("hasPermi"),we=X("loading");return s(),B("div",je,[w(e(H,{model:n(p),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(r,{label:"套餐名称",prop:"packageName"},{default:a(()=>[e(c,{modelValue:n(p).packageName,"onUpdate:modelValue":t[0]||(t[0]=l=>n(p).packageName=l),placeholder:"请输入套餐名称",clearable:"",style:{width:"200px"},onKeyup:Ie(O,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"套餐类型",prop:"packageType"},{default:a(()=>[e(_,{modelValue:n(p).packageType,"onUpdate:modelValue":t[1]||(t[1]=l=>n(p).packageType=l),placeholder:"套餐类型",clearable:"",style:{width:"200px"}},{default:a(()=>[(s(!0),B(Y,null,Z(n(W),l=>(s(),f(g,{key:l.value,label:l.label,value:parseInt(l.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"场库",prop:"warehouseId"},{default:a(()=>[e(G,{modelValue:n(p).warehouseId,"onUpdate:modelValue":t[2]||(t[2]=l=>n(p).warehouseId=l),options:n(S),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(r,null,{default:a(()=>[e(k,{type:"primary",icon:"Search",onClick:O},{default:a(()=>[d("搜索")]),_:1}),e(k,{icon:"Refresh",onClick:ce},{default:a(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ce,n(z)]]),e(b,{gutter:10,class:"mb8"},{default:a(()=>[e(v,{span:1.5},{default:a(()=>[w((s(),f(k,{type:"primary",plain:"",icon:"Plus",onClick:ge},{default:a(()=>[d("新增")]),_:1})),[[P,["vip:package:add"]]])]),_:1}),e(v,{span:1.5},{default:a(()=>[w((s(),f(k,{type:"success",plain:"",icon:"Edit",disabled:n(j),onClick:M},{default:a(()=>[d("修改")]),_:1},8,["disabled"])),[[P,["vip:package:edit"]]])]),_:1}),e(v,{span:1.5},{default:a(()=>[w((s(),f(k,{type:"danger",plain:"",icon:"Delete",disabled:n(F),onClick:J},{default:a(()=>[d("删除")]),_:1},8,["disabled"])),[[P,["vip:package:remove"]]])]),_:1}),e(v,{span:1.5},{default:a(()=>[w((s(),f(k,{type:"warning",plain:"",icon:"Download",onClick:_e},{default:a(()=>[d("导出")]),_:1})),[[P,["vip:package:export"]]])]),_:1}),e(he,{showSearch:n(z),"onUpdate:showSearch":t[3]||(t[3]=l=>ee(z)?z.value=l:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),w((s(),f(ve,{data:n(E),onSelectionChange:fe},{default:a(()=>[e(N,{type:"selection",width:"55",align:"center"}),e(N,{label:"套餐名称",align:"center",prop:"packageName",width:"160","show-overflow-tooltip":!0},{default:a(l=>[e(x,{type:"warning",effect:"light",size:"small"},{default:a(()=>[Fe,d(" "+U(l.row.packageName),1)]),_:2},1024)]),_:1}),e(N,{label:"场库/停车场",align:"center",prop:"warehouseName"},{default:a(l=>[C("div",Qe,[e(x,{type:ie(l.row),effect:"light",size:"small",title:l.row.warehouseName},{default:a(()=>[C("i",{class:ze(se(l.row))},null,2),d(" "+U(pe(l.row.warehouseName,8)),1)]),_:2},1032,["type","title"])])]),_:1}),e(N,{label:"套餐类型",align:"center",prop:"packageType"},{default:a(l=>[e(ke,{options:n(W),value:l.row.packageType},null,8,["options","value"])]),_:1}),e(N,{label:"套餐价格",align:"center",prop:"packagePrice"},{default:a(l=>[C("span",Ke,"¥"+U(l.row.packagePrice||"0.00"),1)]),_:1}),e(N,{label:"会员时长",align:"center",prop:"packageType"},{default:a(l=>[l.row.packageType===30?(s(),f(x,{key:0,type:"warning",effect:"light",size:"small"},{default:a(()=>[d(" 包月 ")]),_:1})):l.row.packageType===365?(s(),f(x,{key:1,type:"success",effect:"light",size:"small"},{default:a(()=>[d(" 包年 ")]),_:1})):(s(),f(x,{key:2,type:"primary",effect:"light",size:"small"},{default:a(()=>[d(U(l.row.packageType)+"天 ",1)]),_:2},1024))]),_:1}),e(N,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(l=>[w((s(),f(k,{link:"",type:"primary",icon:"Edit",onClick:Ve=>M(l.row)},{default:a(()=>[d("修改")]),_:2},1032,["onClick"])),[[P,["vip:package:edit"]]]),w((s(),f(k,{link:"",type:"primary",icon:"Delete",onClick:Ve=>J(l.row)},{default:a(()=>[d("删除")]),_:2},1032,["onClick"])),[[P,["vip:package:remove"]]])]),_:1})]),_:1},8,["data"])),[[we,n(D)]]),e(te,{total:n(Q),"current-page":n(p).pageNum,"onUpdate:currentPage":t[4]||(t[4]=l=>n(p).pageNum=l),"page-size":n(p).pageSize,"onUpdate:pageSize":t[5]||(t[5]=l=>n(p).pageSize=l),onPagination:V},null,8,["total","current-page","page-size"]),e(be,{title:n($),modelValue:n(y),"onUpdate:modelValue":t[11]||(t[11]=l=>ee(y)?y.value=l:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[C("div",Ae,[e(k,{type:"primary",onClick:me},{default:a(()=>[d("确 定")]),_:1}),e(k,{onClick:de},{default:a(()=>[d("取 消")]),_:1})])]),default:a(()=>[e(H,{ref:"packageRef",model:n(u),rules:n(re),"label-width":"100px"},{default:a(()=>[e(b,null,{default:a(()=>[e(v,{span:24},{default:a(()=>[e(r,{label:"套餐名称",prop:"packageName"},{default:a(()=>[e(c,{modelValue:n(u).packageName,"onUpdate:modelValue":t[6]||(t[6]=l=>n(u).packageName=l),placeholder:"请输入套餐名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>[e(v,{span:24},{default:a(()=>[e(r,{label:"场库/停车场",prop:"warehouseId"},{default:a(()=>[e(G,{ref_key:"cascaderRef",ref:ne,modelValue:n(u).warehouseId,"onUpdate:modelValue":t[7]||(t[7]=l=>n(u).warehouseId=l),options:n(S),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>[e(v,{span:12},{default:a(()=>[e(r,{label:"套餐类型",prop:"packageType"},{default:a(()=>[e(_,{modelValue:n(u).packageType,"onUpdate:modelValue":t[8]||(t[8]=l=>n(u).packageType=l),placeholder:"请选择套餐类型"},{default:a(()=>[(s(!0),B(Y,null,Z(n(W),l=>(s(),f(g,{key:l.value,label:l.label,value:parseInt(l.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b),n(u).packageType?(s(),f(b,{key:0},{default:a(()=>[e(v,{span:12},{default:a(()=>[e(r,{label:"会员时长"},{default:a(()=>[e(c,{value:n(u).packageType+"天",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})):Ue("",!0),e(b,null,{default:a(()=>[e(v,{span:12},{default:a(()=>[e(r,{label:"套餐价格",prop:"packagePrice"},{default:a(()=>[e(ye,{modelValue:n(u).packagePrice,"onUpdate:modelValue":t[9]||(t[9]=l=>n(u).packagePrice=l),min:0,precision:2,placeholder:"请输入套餐价格"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b),e(r,{label:"备注",prop:"remark"},{default:a(()=>[e(c,{modelValue:n(u).remark,"onUpdate:modelValue":t[10]||(t[10]=l=>n(u).remark=l),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Xe=Ne(Me,[["__scopeId","data-v-d64d8d34"]]);export{Xe as default};
