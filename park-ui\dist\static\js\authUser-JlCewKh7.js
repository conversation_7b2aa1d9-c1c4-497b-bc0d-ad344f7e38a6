import{M as F,u as M,g as G,r as d,A as H,d as s,O as P,c as J,o as _,P as h,e,Q as W,k as o,f as t,l as V,q as m,i as w,N as X,h as Y,t as Z}from"./index-kDFdMZgN.js";import ee from"./selectUser-DEpGhCIY.js";import{a as oe,b as te,c as le}from"./role-D90S1TPl.js";import{C as $}from"./index-D3uM1NSj.js";const ne={class:"app-container"},ae=F({name:"AuthUser"}),de=Object.assign(ae,{components:{CustomPagination:$}},{setup(re){const x=M(),{proxy:u}=G(),{sys_normal_disable:R}=u.useDict("sys_normal_disable"),S=d([]),y=d(!0),g=d(!0),N=d(!0),k=d(0),U=d([]),n=H({pageNum:1,pageSize:10,roleId:x.params.roleId,userName:void 0,phonenumber:void 0});function c(){y.value=!0,oe(n).then(r=>{S.value=r.rows,k.value=r.total,y.value=!1})}function z(){const r={path:"/system/role"};u.$tab.closeOpenPage(r)}function f(){n.pageNum=1,c()}function A(){u.resetForm("queryRef"),f()}function T(r){U.value=r.map(a=>a.userId),N.value=!r.length}function q(){u.$refs.selectRef.show()}function B(r){u.$modal.confirm('确认要取消该用户"'+r.userName+'"角色吗？').then(function(){return le({userId:r.userId,roleId:n.roleId})}).then(()=>{c(),u.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}function D(r){const a=n.roleId,b=U.value.join(",");u.$modal.confirm("是否取消选中用户授权数据项?").then(function(){return te({roleId:a,userIds:b})}).then(()=>{c(),u.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}return c(),(r,a)=>{const b=s("el-input"),v=s("el-form-item"),p=s("el-button"),O=s("el-form"),C=s("el-col"),Q=s("right-toolbar"),j=s("el-row"),i=s("el-table-column"),K=s("dict-tag"),L=s("el-table"),I=P("hasPermi"),E=P("loading");return _(),J("div",ne,[h(e(O,{model:o(n),ref:"queryRef",inline:!0},{default:t(()=>[e(v,{label:"用户名称",prop:"userName"},{default:t(()=>[e(b,{modelValue:o(n).userName,"onUpdate:modelValue":a[0]||(a[0]=l=>o(n).userName=l),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:V(f,["enter"])},null,8,["modelValue"])]),_:1}),e(v,{label:"手机号码",prop:"phonenumber"},{default:t(()=>[e(b,{modelValue:o(n).phonenumber,"onUpdate:modelValue":a[1]||(a[1]=l=>o(n).phonenumber=l),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:V(f,["enter"])},null,8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(p,{type:"primary",icon:"Search",onClick:f},{default:t(()=>[m("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:A},{default:t(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[W,o(g)]]),e(j,{gutter:10,class:"mb8"},{default:t(()=>[e(C,{span:1.5},{default:t(()=>[h((_(),w(p,{type:"primary",plain:"",icon:"Plus",onClick:q},{default:t(()=>[m("添加用户")]),_:1})),[[I,["system:role:add"]]])]),_:1}),e(C,{span:1.5},{default:t(()=>[h((_(),w(p,{type:"danger",plain:"",icon:"CircleClose",disabled:o(N),onClick:D},{default:t(()=>[m("批量取消授权")]),_:1},8,["disabled"])),[[I,["system:role:remove"]]])]),_:1}),e(C,{span:1.5},{default:t(()=>[e(p,{type:"warning",plain:"",icon:"Close",onClick:z},{default:t(()=>[m("关闭")]),_:1})]),_:1}),e(Q,{showSearch:o(g),"onUpdate:showSearch":a[2]||(a[2]=l=>X(g)?g.value=l:null),onQueryTable:c},null,8,["showSearch"])]),_:1}),h((_(),w(L,{data:o(S),onSelectionChange:T},{default:t(()=>[e(i,{type:"selection",width:"55",align:"center"}),e(i,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),e(i,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),e(i,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),e(i,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),e(i,{label:"状态",align:"center",prop:"status"},{default:t(l=>[e(K,{options:o(R),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(l=>[Y("span",null,Z(r.parseTime(l.row.createTime)),1)]),_:1}),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(l=>[h((_(),w(p,{link:"",type:"primary",icon:"CircleClose",onClick:se=>B(l.row)},{default:t(()=>[m("取消授权")]),_:2},1032,["onClick"])),[[I,["system:role:remove"]]])]),_:1})]),_:1},8,["data"])),[[E,o(y)]]),e($,{total:o(k),"current-page":o(n).pageNum,"onUpdate:currentPage":a[3]||(a[3]=l=>o(n).pageNum=l),"page-size":o(n).pageSize,"onUpdate:pageSize":a[4]||(a[4]=l=>o(n).pageSize=l),onPagination:c},null,8,["total","current-page","page-size"]),e(o(ee),{ref:"selectRef",roleId:o(n).roleId,onOk:f},null,8,["roleId"])])}}});export{de as default};
