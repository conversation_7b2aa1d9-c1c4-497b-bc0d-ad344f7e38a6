import{_ as be,M as ve,g as ye,r as _,A as we,R as ke,p as Ce,d as u,O as A,c as p,o as r,P as k,e,Q as Ne,f as l,l as xe,k as t,J as De,K as Te,i as h,q as d,h as b,t as c,Z as Ve,y as Se,z as Ie}from"./index-kDFdMZgN.js";import{g as Le}from"./member-Bwl49t4m.js";import{l as Pe,d as Ue}from"./exceptionOrder-BxTjqic-.js";import{C as J}from"./index-D3uM1NSj.js";const qe=x=>(Se("data-v-1c15beef"),x=x(),Ie(),x),ze={class:"app-container"},Oe={key:1},Ee={class:"error-type-wrapper"},Me={key:0},Re={key:1},$e={key:0},Be={key:1},Qe={key:0,class:"money-text"},Ye={key:1},Ke={class:"error-detail-view"},We={key:1},je={key:0,style:{color:"#f56c6c","font-weight":"bold"}},Fe={key:1},Ae={class:"image-section"},Je=qe(()=>b("h3",{style:{"margin-bottom":"15px",color:"#303133"}},"相关图片",-1)),Ze={class:"image-container"},Ge={key:0,class:"image-item"},He={key:1,class:"no-image"},Xe={class:"dialog-footer"},el=ve({name:"ErrorDataLog"}),ll=Object.assign(el,{components:{CustomPagination:J}},{setup(x){const{proxy:C}=ye(),{error_data_code:I}=C.useDict("error_data_code"),z=_([]),D=_(!1),L=_(!0),P=_(!0),O=_([]),Z=_(!0),E=_(!0),M=_(0),G=_(""),y=_(""),H=_([]),R=_([]),$=_(!1),X=we({form:{},queryParams:{pageNum:1,pageSize:10,plateNum:null,parkingId:null,errCode:null,beginTime:null,endTime:null},rules:{plateNum:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],parkingId:[{required:!0,message:"场库ID不能为空",trigger:"change"}],errCode:[{required:!0,message:"错误类型不能为空",trigger:"change"}]}}),{queryParams:s,form:i,rules:al}=ke(X);function N(){L.value=!0,s.value.params={},y.value?(s.value.params.beginTime=y.value+" 00:00:00",s.value.params.endTime=y.value+" 23:59:59"):(s.value.params.beginTime=null,s.value.params.endTime=null),Pe(s.value).then(n=>{z.value=n.rows,M.value=n.total,L.value=!1})}function U(){s.value.pageNum=1,N()}function ee(){y.value="",C.resetForm("queryRef"),Object.assign(s.value,{pageNum:1,pageSize:10,plateNum:null,parkingId:null,errCode:null,beginTime:null,endTime:null}),U()}function le(n){O.value=n.map(o=>o.id),Z.value=n.length!=1,E.value=!n.length}function ae(n){B(),$.value=!0,i.value={...n},D.value=!0}function te(){C.download("system/errorDataLog/export",{...s.value},`错误数据日志_${new Date().getTime()}.xlsx`)}function B(){i.value={},$.value=!0}function ne(){D.value=!1,B()}function Q(n){const o=n.id||O.value;C.$modal.confirm('是否确认删除记录ID为"'+o+'"的数据项？').then(function(){return Ue(o)}).then(()=>{N(),C.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){Le().then(n=>{H.value=n.data||[],R.value=re(n.data)})}function re(n){if(!n||n.length===0)return[];const o=n.filter(m=>m.parentId==="0"),q=n.filter(m=>m.parentId!=="0");return o.map(m=>{const T=q.filter(w=>w.parentId===m.id).map(w=>({value:w.id,label:w.warehouseName,isLeaf:!0}));return{value:m.id,label:m.warehouseName,children:T.length>0?T:void 0}})}function Y(n){return n?n.length===8?"success":"primary":"info"}function K(n){return n?n.length===8?"#d4edda":"#cce7ff":"#909399"}return Ce(()=>{N(),oe()}),(n,o)=>{const q=u("el-input"),m=u("el-form-item"),T=u("el-cascader"),w=u("el-option"),ie=u("el-select"),se=u("el-date-picker"),v=u("el-button"),ue=u("el-form"),V=u("el-col"),de=u("right-toolbar"),W=u("el-row"),f=u("el-table-column"),j=u("el-tag"),F=u("dict-tag"),ce=u("el-table"),g=u("el-descriptions-item"),pe=u("el-descriptions"),me=u("el-image"),_e=u("el-empty"),ge=u("el-dialog"),S=A("hasPermi"),fe=A("loading");return r(),p("div",ze,[k(e(ue,{model:t(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(m,{label:"车牌号",prop:"plateNum"},{default:l(()=>[e(q,{modelValue:t(s).plateNum,"onUpdate:modelValue":o[0]||(o[0]=a=>t(s).plateNum=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:xe(U,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"场库",prop:"parkingId"},{default:l(()=>[e(T,{modelValue:t(s).parkingId,"onUpdate:modelValue":o[1]||(o[1]=a=>t(s).parkingId=a),options:R.value,props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(m,{label:"错误类型",prop:"errCode"},{default:l(()=>[e(ie,{modelValue:t(s).errCode,"onUpdate:modelValue":o[2]||(o[2]=a=>t(s).errCode=a),placeholder:"请选择错误类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(r(!0),p(De,null,Te(t(I),a=>(r(),h(w,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"更新时间",prop:"updateDate"},{default:l(()=>[e(se,{modelValue:y.value,"onUpdate:modelValue":o[3]||(o[3]=a=>y.value=a),style:{width:"200px"},"value-format":"YYYY-MM-DD",type:"date",placeholder:"选择更新日期"},null,8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(v,{type:"primary",icon:"Search",onClick:U},{default:l(()=>[d("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:ee},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ne,P.value]]),e(W,{gutter:10,class:"mb8"},{default:l(()=>[e(V,{span:1.5},{default:l(()=>[k((r(),h(v,{type:"danger",plain:"",icon:"Delete",disabled:E.value,onClick:Q},{default:l(()=>[d("删除")]),_:1},8,["disabled"])),[[S,["system:errorDataLog:remove"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[k((r(),h(v,{type:"warning",plain:"",icon:"Download",onClick:te},{default:l(()=>[d("导出")]),_:1})),[[S,["system:errorDataLog:export"]]])]),_:1}),e(de,{showSearch:P.value,"onUpdate:showSearch":o[4]||(o[4]=a=>P.value=a),onQueryTable:N},null,8,["showSearch"])]),_:1}),k((r(),h(ce,{data:z.value,onSelectionChange:le},{default:l(()=>[e(f,{type:"selection",width:"55",align:"center"}),e(f,{label:"场库名称",align:"center",prop:"parkingName","min-width":"120"},{default:l(a=>[b("span",null,c(a.row.parkingName||"--"),1)]),_:1}),e(f,{label:"车牌号",align:"center",prop:"plateNum",width:"120"},{default:l(a=>[a.row.plateNum?(r(),h(j,{key:0,type:Y(a.row.plateNum),color:K(a.row.plateNum),effect:"plain"},{default:l(()=>[d(c(a.row.plateNum),1)]),_:2},1032,["type","color"])):(r(),p("span",Oe,"--"))]),_:1}),e(f,{label:"错误类型",align:"center",prop:"errCode",width:"120"},{default:l(a=>[b("div",Ee,[e(F,{options:t(I),value:a.row.errCode},null,8,["options","value"])])]),_:1}),e(f,{label:"入场时间",align:"center",prop:"inTime","min-width":"150"},{default:l(a=>[a.row.inTime?(r(),p("span",Me,c(a.row.inTime),1)):(r(),p("span",Re,"--"))]),_:1}),e(f,{label:"出场时间",align:"center",prop:"outTime","min-width":"150"},{default:l(a=>[a.row.outTime?(r(),p("span",$e,c(a.row.outTime),1)):(r(),p("span",Be,"--"))]),_:1}),e(f,{label:"金额",align:"center",prop:"money",width:"100"},{default:l(a=>[a.row.money!==null&&a.row.money!==void 0?(r(),p("span",Qe,"¥"+c(a.row.money),1)):(r(),p("span",Ye,"--"))]),_:1}),e(f,{label:"备注",align:"center",prop:"remark","min-width":"200","show-overflow-tooltip":""},{default:l(a=>[b("span",null,c(a.row.remark||"--"),1)]),_:1}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"160"},{default:l(a=>[k((r(),h(v,{link:"",type:"primary",icon:"View",onClick:he=>ae(a.row)},{default:l(()=>[d("查看")]),_:2},1032,["onClick"])),[[S,["system:errorDataLog:query"]]]),k((r(),h(v,{link:"",type:"primary",icon:"Delete",onClick:he=>Q(a.row)},{default:l(()=>[d("删除")]),_:2},1032,["onClick"])),[[S,["system:errorDataLog:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,L.value]]),e(J,{total:M.value,"current-page":t(s).pageNum,"onUpdate:currentPage":o[5]||(o[5]=a=>t(s).pageNum=a),"page-size":t(s).pageSize,"onUpdate:pageSize":o[6]||(o[6]=a=>t(s).pageSize=a),onPagination:N},null,8,["total","current-page","page-size"]),e(ge,{title:G.value,modelValue:D.value,"onUpdate:modelValue":o[7]||(o[7]=a=>D.value=a),width:"1000px","append-to-body":""},{footer:l(()=>[b("div",Xe,[e(v,{onClick:ne},{default:l(()=>[d("关 闭")]),_:1})])]),default:l(()=>[b("div",Ke,[e(W,{gutter:20},{default:l(()=>[e(V,{span:14},{default:l(()=>[e(pe,{title:"错误数据日志详情",column:2,border:""},{default:l(()=>[e(g,{label:"场库名称","label-align":"right","label-class-name":"desc-label",span:2},{default:l(()=>[d(c(t(i).parkingName||"--"),1)]),_:1}),e(g,{label:"车牌号","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(i).plateNum?(r(),h(j,{key:0,type:Y(t(i).plateNum),color:K(t(i).plateNum),effect:"plain"},{default:l(()=>[d(c(t(i).plateNum),1)]),_:1},8,["type","color"])):(r(),p("span",We,"--"))]),_:1}),e(g,{label:"错误类型","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[e(F,{options:t(I),value:t(i).errCode},null,8,["options","value"])]),_:1}),e(g,{label:"入场时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[d(c(t(i).inTime||"--"),1)]),_:1}),e(g,{label:"出场时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[d(c(t(i).outTime||"--"),1)]),_:1}),e(g,{label:"入场通道","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[d(c(t(i).inChannelName||"--"),1)]),_:1}),e(g,{label:"出场通道","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[d(c(t(i).outChannelName||"--"),1)]),_:1}),e(g,{label:"金额","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(i).money!==null&&t(i).money!==void 0?(r(),p("span",je," ¥"+c(t(i).money),1)):(r(),p("span",Fe,"--"))]),_:1}),e(g,{label:"更新时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[d(c(t(i).lastUpdate?t(Ve)(t(i).lastUpdate,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1}),e(g,{label:"备注","label-align":"right","label-class-name":"desc-label",span:2},{default:l(()=>[d(c(t(i).remark||"--"),1)]),_:1})]),_:1})]),_:1}),e(V,{span:10},{default:l(()=>[b("div",Ae,[Je,b("div",Ze,[t(i).imgPath?(r(),p("div",Ge,[e(me,{src:t(i).imgPath,fit:"cover",style:{width:"100%",height:"300px","border-radius":"4px"},"preview-src-list":[t(i).imgPath],"preview-teleported":""},null,8,["src","preview-src-list"])])):(r(),p("div",He,[e(_e,{description:"暂无图片"})]))])])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"])])}}}),il=be(ll,[["__scopeId","data-v-1c15beef"]]);export{il as default};
