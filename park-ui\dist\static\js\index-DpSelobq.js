import{s as U,_ as Se,M as Ve,g as qe,r as y,A as $e,R as Fe,w as Ue,B as L,d as s,O as ee,c as H,o as p,P as v,e as l,Q as te,k as a,f as t,l as De,J as le,K as ne,i as b,q as d,N as B,h,t as F,y as Ee,z as Ne}from"./index-kDFdMZgN.js";function ae(m){return U({url:"/system/agreement/list",method:"get",params:m})}function oe(m){return U({url:"/system/agreement/"+m,method:"get"})}function Ae(m){return U({url:"/system/agreement",method:"post",data:m})}function re(m){return U({url:"/system/agreement",method:"put",data:m})}function Re(m){return U({url:"/system/agreement/"+m,method:"delete"})}const ze=m=>(Ee("data-v-96febfef"),m=m(),Ne(),m),Ie={class:"app-container"},Pe={class:"edit-dialog-container"},Le={class:"form-header"},He={class:"editor-container"},Be=ze(()=>h("div",{class:"editor-label"},"协议内容",-1)),Oe={class:"editor-wrapper"},Me={class:"dialog-footer"},Qe={class:"agreement-detail-container"},Ke={class:"agreement-content-wrapper"},We=["innerHTML"],Ye={class:"dialog-footer"},je=Ve({name:"Agreement"}),Je=Object.assign(je,{setup(m){const{proxy:u}=qe(),{agreement_type:D}=u.useDict("agreement_type"),O=y([]),w=y(!1),V=y(!1),C=y(!0),E=y(!0),A=y([]),M=y(!0),Q=y(!0),R=y(0),z=y(""),ie=$e({form:{},viewForm:{},queryParams:{pageNum:1,pageSize:10,agreementTitle:null,agreementType:null,deleteFlag:0},rules:{agreementType:[{required:!0,message:"协议类型不能为空",trigger:"change"}],agreementTitle:[{required:!0,message:"协议标题不能为空",trigger:"blur"}],agreementContent:[{required:!0,message:"协议内容不能为空",trigger:"blur"}]}}),{queryParams:g,form:f,viewForm:k,rules:se}=Fe(ie),de=y({placeholder:"请输入协议内容...",scroll:!0,maxLength:5e4,MENU_CONF:{fontSize:{fontSizeList:["12px","14px","16px","18px","20px","24px","28px","32px"]},fontFamily:{fontFamilyList:["黑体","仿宋","楷体","标楷体","华文仿宋","华文楷体","宋体","Arial","Tahoma","Verdana"]}}});function x(){C.value=!0,ae(g.value).then(o=>{O.value=o.rows,R.value=o.total,C.value=!1})}function ue(){w.value=!1,I()}function I(){f.value={id:null,agreementType:null,agreementTitle:null,agreementContent:null,deleteFlag:null,createTime:null,updateTime:null},u.resetForm("agreementRef")}function P(){g.value.pageNum=1,x()}function me(){u.resetForm("queryRef"),P()}function pe(o){A.value=o.map(e=>e.id),M.value=o.length!=1,Q.value=!o.length}function ce(){I(),w.value=!0,z.value="添加系统协议",L(()=>{q()})}function K(o){I();const e=o.id||A.value;oe(e).then(r=>{f.value=r.data,w.value=!0,z.value="修改系统协议",L(()=>{q()})})}function ge(o){k.value={};const e=o.id;oe(e).then(r=>{k.value=r.data,V.value=!0})}function fe(){u.$refs.agreementRef.validate(o=>{o&&(f.value.id!=null?re(f.value).then(e=>{u.$modal.msgSuccess("修改成功"),w.value=!1,x()}).catch(e=>{var r,i;console.error("更新协议失败:",e),u.$modal.msgError("更新失败："+(((i=(r=e.response)==null?void 0:r.data)==null?void 0:i.msg)||e.message))}):Ae(f.value).then(e=>{u.$modal.msgSuccess("新增成功"),w.value=!1,x()}).catch(e=>{var r,i;console.error("新增协议失败:",e),u.$modal.msgError("新增失败："+(((i=(r=e.response)==null?void 0:r.data)==null?void 0:i.msg)||e.message))}))})}function W(o){const e=o.id||A.value;u.$modal.confirm('是否确认删除系统协议编号为"'+e+'"的数据项？').then(function(){return Re(e)}).then(()=>{x(),u.$modal.msgSuccess("删除成功")}).catch(()=>{})}function _e(){u.download("system/agreement/export",{...g.value},`agreement_${new Date().getTime()}.xlsx`)}function Y(o){return o?o.replace(/<p[^>]*>\s*<\/p>/gi,"").replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi,"").replace(/<p([^>]*)>/gi,'<p$1 style="margin: 8px 0; line-height: 1.6;">').replace(/text-align:\s*center/gi,"text-align: center !important").replace(/text-align:\s*left/gi,"text-align: left !important").replace(/text-align:\s*right/gi,"text-align: right !important").replace(/&nbsp;{4,}/g,"&nbsp;&nbsp;&nbsp;").replace(/<strong([^>]*)>/gi,'<strong$1 style="font-weight: bold;">').replace(/\s{3,}/g," "):""}function q(){setTimeout(()=>{const o=document.querySelector(".editor-wrapper");if(o){const e=o.querySelector(".editor");e&&(e.style.height="100%",e.style.display="flex",e.style.flexDirection="column");const r=o.querySelector(".ql-container");r&&(r.style.flex="1",r.style.overflowY="auto",r.style.height="auto",r.style.maxHeight="none");const i=o.querySelector(".ql-editor");i&&(i.style.overflowY="auto",i.style.maxHeight="none",i.style.height="auto",i.style.minHeight="200px");const _=o.querySelector(".ql-toolbar");_&&(_.style.position="sticky",_.style.top="0",_.style.zIndex="100",_.style.backgroundColor="#fff",_.style.flexShrink="0")}},500)}function he(){u.$modal.confirm("此操作将清理所有协议的格式问题（移除多余空行），是否继续？").then(function(){C.value=!0,ae({pageNum:1,pageSize:1e3,deleteFlag:0}).then(o=>{const e=o.rows;let r=[];e.forEach(i=>{if(i.agreementContent){const _=Y(i.agreementContent);_!==i.agreementContent&&(i.agreementContent=_,r.push(re(i)))}}),r.length>0?Promise.all(r).then(()=>{u.$modal.msgSuccess(`成功清理了 ${r.length} 个协议的格式`),x()}).catch(()=>{u.$modal.msgError("清理格式时发生错误")}).finally(()=>{C.value=!1}):(u.$modal.msgInfo("没有需要清理的内容"),C.value=!1)}).catch(()=>{C.value=!1})}).catch(()=>{})}return x(),Ue(w,o=>{o&&L(()=>{q(),setTimeout(()=>q(),1e3),setTimeout(()=>q(),2e3)})}),(o,e)=>{const r=s("el-input"),i=s("el-form-item"),_=s("el-option"),j=s("el-select"),c=s("el-button"),J=s("el-form"),$=s("el-col"),ye=s("right-toolbar"),ve=s("el-row"),S=s("el-table-column"),G=s("dict-tag"),be=s("el-table"),we=s("pagination"),xe=s("editor"),X=s("el-dialog"),N=s("el-descriptions-item"),Te=s("el-descriptions"),Ce=s("el-divider"),T=ee("hasPermi"),ke=ee("loading");return p(),H("div",Ie,[v(l(J,{model:a(g),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[l(i,{label:"协议标题",prop:"agreementTitle"},{default:t(()=>[l(r,{modelValue:a(g).agreementTitle,"onUpdate:modelValue":e[0]||(e[0]=n=>a(g).agreementTitle=n),placeholder:"请输入协议标题",clearable:"",onKeyup:De(P,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"协议类型",prop:"agreementType"},{default:t(()=>[l(j,{modelValue:a(g).agreementType,"onUpdate:modelValue":e[1]||(e[1]=n=>a(g).agreementType=n),placeholder:"请选择协议类型",clearable:""},{default:t(()=>[(p(!0),H(le,null,ne(a(D),n=>(p(),b(_,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:t(()=>[l(c,{type:"primary",icon:"Search",onClick:P},{default:t(()=>[d("搜索")]),_:1}),l(c,{icon:"Refresh",onClick:me},{default:t(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[te,a(E)]]),l(ve,{gutter:10,class:"mb8"},{default:t(()=>[l($,{span:1.5},{default:t(()=>[v((p(),b(c,{type:"primary",plain:"",icon:"Plus",onClick:ce},{default:t(()=>[d("新增")]),_:1})),[[T,["system:agreement:add"]]])]),_:1}),l($,{span:1.5},{default:t(()=>[v((p(),b(c,{type:"success",plain:"",icon:"Edit",disabled:a(M),onClick:K},{default:t(()=>[d("修改")]),_:1},8,["disabled"])),[[T,["system:agreement:edit"]]])]),_:1}),l($,{span:1.5},{default:t(()=>[v((p(),b(c,{type:"danger",plain:"",icon:"Delete",disabled:a(Q),onClick:W},{default:t(()=>[d("删除")]),_:1},8,["disabled"])),[[T,["system:agreement:remove"]]])]),_:1}),l($,{span:1.5},{default:t(()=>[v((p(),b(c,{type:"warning",plain:"",icon:"Download",onClick:_e},{default:t(()=>[d("导出")]),_:1})),[[T,["system:agreement:export"]]])]),_:1}),l($,{span:1.5},{default:t(()=>[v((p(),b(c,{type:"info",plain:"",icon:"Refresh",onClick:he},{default:t(()=>[d("清理格式")]),_:1})),[[T,["system:agreement:edit"]]])]),_:1}),l(ye,{showSearch:a(E),"onUpdate:showSearch":e[2]||(e[2]=n=>B(E)?E.value=n:null),onQueryTable:x},null,8,["showSearch"])]),_:1}),v((p(),b(be,{data:a(O),onSelectionChange:pe},{default:t(()=>[l(S,{type:"selection",width:"55",align:"center"}),l(S,{label:"协议ID",align:"center",prop:"id"}),l(S,{label:"协议标题",align:"center",prop:"agreementTitle","show-overflow-tooltip":!0}),l(S,{label:"协议类型",align:"center",prop:"agreementType"},{default:t(n=>[l(G,{options:a(D),value:n.row.agreementType},null,8,["options","value"])]),_:1}),l(S,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(n=>[h("span",null,F(o.parseTime(n.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(S,{label:"更新时间",align:"center",prop:"updateTime",width:"180"},{default:t(n=>[h("span",null,F(o.parseTime(n.row.updateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(S,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(n=>[v((p(),b(c,{link:"",type:"primary",icon:"View",onClick:Z=>ge(n.row)},{default:t(()=>[d("查看")]),_:2},1032,["onClick"])),[[T,["system:agreement:query"]]]),v((p(),b(c,{link:"",type:"primary",icon:"Edit",onClick:Z=>K(n.row)},{default:t(()=>[d("修改")]),_:2},1032,["onClick"])),[[T,["system:agreement:edit"]]]),v((p(),b(c,{link:"",type:"primary",icon:"Delete",onClick:Z=>W(n.row)},{default:t(()=>[d("删除")]),_:2},1032,["onClick"])),[[T,["system:agreement:remove"]]])]),_:1})]),_:1},8,["data"])),[[ke,a(C)]]),v(l(we,{total:a(R),page:a(g).pageNum,"onUpdate:page":e[3]||(e[3]=n=>a(g).pageNum=n),limit:a(g).pageSize,"onUpdate:limit":e[4]||(e[4]=n=>a(g).pageSize=n),onPagination:x},null,8,["total","page","limit"]),[[te,a(R)>0]]),l(X,{title:a(z),modelValue:a(w),"onUpdate:modelValue":e[8]||(e[8]=n=>B(w)?w.value=n:null),width:"90%",top:"5vh","append-to-body":"",class:"edit-agreement-dialog",style:{maxWidth:"1200px",height:"85vh",maxHeight:"85vh"},"close-on-click-modal":!1},{footer:t(()=>[h("div",Me,[l(c,{type:"primary",onClick:fe},{default:t(()=>[d("确 定")]),_:1}),l(c,{onClick:ue},{default:t(()=>[d("取 消")]),_:1})])]),default:t(()=>[h("div",Pe,[h("div",Le,[l(J,{ref:"agreementRef",model:a(f),rules:a(se),"label-width":"80px"},{default:t(()=>[l(i,{label:"协议类型",prop:"agreementType"},{default:t(()=>[l(j,{modelValue:a(f).agreementType,"onUpdate:modelValue":e[5]||(e[5]=n=>a(f).agreementType=n),placeholder:"请选择协议类型"},{default:t(()=>[(p(!0),H(le,null,ne(a(D),n=>(p(),b(_,{key:n.value,label:n.label,value:parseInt(n.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"协议标题",prop:"agreementTitle"},{default:t(()=>[l(r,{modelValue:a(f).agreementTitle,"onUpdate:modelValue":e[6]||(e[6]=n=>a(f).agreementTitle=n),placeholder:"请输入协议标题"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),h("div",He,[Be,h("div",Oe,[l(xe,{modelValue:a(f).agreementContent,"onUpdate:modelValue":e[7]||(e[7]=n=>a(f).agreementContent=n),"min-height":200,height:null,options:a(de),style:{height:"100%",overflow:"visible"}},null,8,["modelValue","options"])])])])]),_:1},8,["title","modelValue"]),l(X,{title:"协议详情",modelValue:a(V),"onUpdate:modelValue":e[10]||(e[10]=n=>B(V)?V.value=n:null),width:"90%",top:"5vh","append-to-body":"","destroy-on-close":"",class:"agreement-dialog",style:{maxWidth:"1200px",height:"85vh",maxHeight:"85vh"},"close-on-click-modal":!1},{footer:t(()=>[h("div",Ye,[l(c,{onClick:e[9]||(e[9]=n=>V.value=!1)},{default:t(()=>[d("关 闭")]),_:1})])]),default:t(()=>[h("div",Qe,[l(Te,{column:2,border:"",class:"agreement-info"},{default:t(()=>[l(N,{label:"协议标题"},{default:t(()=>[d(F(a(k).agreementTitle),1)]),_:1}),l(N,{label:"协议类型"},{default:t(()=>[l(G,{options:a(D),value:a(k).agreementType},null,8,["options","value"])]),_:1}),l(N,{label:"创建时间"},{default:t(()=>[d(F(o.parseTime(a(k).createTime)),1)]),_:1}),l(N,{label:"更新时间"},{default:t(()=>[d(F(o.parseTime(a(k).updateTime)),1)]),_:1})]),_:1}),l(Ce,{"content-position":"left"},{default:t(()=>[d("协议内容")]),_:1}),h("div",Ke,[h("div",{class:"agreement-content",style:{height:"50vh","overflow-y":"auto",padding:"20px",border:"1px solid #dcdfe6","border-radius":"4px","background-color":"#fafafa","line-height":"1.8","font-size":"14px","white-space":"pre-wrap","word-wrap":"break-word"},innerHTML:Y(a(k).agreementContent)},null,8,We)])])]),_:1},8,["modelValue"])])}}}),Xe=Se(Je,[["__scopeId","data-v-96febfef"]]);export{Xe as default};
