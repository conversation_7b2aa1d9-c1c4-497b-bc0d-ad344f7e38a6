import{i as z}from"./index-o0oVz-rl.js";import{_ as G,M as U,g as F,r as i,p as H,C as J,d as l,c as K,o as Q,e as t,f as e,h as s,k as o,$ as W,t as b,a0 as Y,a1 as Z,a2 as tt,q as O,y as et,z as st}from"./index-kDFdMZgN.js";import{a as at,b as nt}from"./exceptionOrder-BxTjqic-.js";const r=m=>(et("data-v-d4f82b7d"),m=m(),st(),m),ot={class:"app-container"},it={class:"statistics-content"},lt={class:"statistics-icon total"},ct={class:"statistics-info"},dt={class:"statistics-number"},rt=r(()=>s("div",{class:"statistics-label"},"异常订单总数",-1)),ut={class:"statistics-content"},_t={class:"statistics-icon pending"},pt={class:"statistics-info"},ft={class:"statistics-number"},ht=r(()=>s("div",{class:"statistics-label"},"待处理订单",-1)),vt={class:"statistics-content"},mt={class:"statistics-icon today"},gt={class:"statistics-info"},yt={class:"statistics-number"},bt=r(()=>s("div",{class:"statistics-label"},"今日新增",-1)),Ct={class:"statistics-content"},wt={class:"statistics-icon processed"},xt={class:"statistics-info"},St={class:"statistics-number"},kt=r(()=>s("div",{class:"statistics-label"},"已处理订单",-1)),zt=r(()=>s("div",{class:"card-header"},[s("span",null,"异常类型分布")],-1)),Ot=r(()=>s("div",{class:"card-header"},[s("span",null,"处理状态分布")],-1)),Dt={class:"card-header"},Nt=r(()=>s("span",null,"异常订单趋势（最近30天）",-1)),Rt=r(()=>s("div",{class:"card-header"},[s("span",null,"场库异常统计")],-1)),Tt=U({name:"ExceptionOrderStatistics"}),Et=Object.assign(Tt,{setup(m){const{proxy:Bt}=F(),f=i({}),D=i([]),C=i([]),N=i([]),w=i([]),h=i(30),R=i(),T=i(),E=i();let u=null,_=null,p=null;function L(){at().then(a=>{f.value=a.data,D.value=a.data.typeStats||[],C.value=a.data.statusStats||[],N.value=a.data.warehouseStats||[],M(),V()})}function B(){nt(h.value).then(a=>{w.value=a.data||[],A()})}function M(){u||(u=z(R.value));const a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"异常类型",type:"pie",radius:"50%",data:D.value.map(n=>({value:n.count,name:n.typeName})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};u.setOption(a)}function V(){_||(_=z(T.value));const a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"处理状态",type:"pie",radius:"50%",data:C.value.map(n=>({value:n.count,name:n.statusName})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};_.setOption(a)}function A(){p||(p=z(E.value));const a={tooltip:{trigger:"axis"},legend:{data:["异常订单数量"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:w.value.map(n=>n.date)},yAxis:{type:"value"},series:[{name:"异常订单数量",type:"line",stack:"Total",data:w.value.map(n=>n.count)}]};p.setOption(a)}function x(a){h.value=a,B()}function P(){const a=C.value.find(n=>n.status===2);return a?a.count:0}function I(a){const n=f.value.totalCount||1;return Math.round(a/n*100)}function $(){u&&u.resize(),_&&_.resize(),p&&p.resize()}return H(()=>{L(),B(),window.addEventListener("resize",$)}),J(()=>{window.removeEventListener("resize",$),u&&u.dispose(),_&&_.dispose(),p&&p.dispose()}),(a,n)=>{const g=l("el-icon"),c=l("el-card"),d=l("el-col"),y=l("el-row"),S=l("el-button"),X=l("el-button-group"),k=l("el-table-column"),j=l("el-progress"),q=l("el-table");return Q(),K("div",ot,[t(y,{gutter:20,class:"statistics-cards"},{default:e(()=>[t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",it,[s("div",lt,[t(g,null,{default:e(()=>[t(o(W))]),_:1})]),s("div",ct,[s("div",dt,b(o(f).totalCount||0),1),rt])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",ut,[s("div",_t,[t(g,null,{default:e(()=>[t(o(Y))]),_:1})]),s("div",pt,[s("div",ft,b(o(f).pendingCount||0),1),ht])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",vt,[s("div",mt,[t(g,null,{default:e(()=>[t(o(Z))]),_:1})]),s("div",gt,[s("div",yt,b(o(f).todayNewCount||0),1),bt])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",Ct,[s("div",wt,[t(g,null,{default:e(()=>[t(o(tt))]),_:1})]),s("div",xt,[s("div",St,b(P()),1),kt])])]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"chart-container"},{default:e(()=>[t(d,{span:12},{default:e(()=>[t(c,null,{header:e(()=>[zt]),default:e(()=>[s("div",{ref_key:"typeChartRef",ref:R,style:{height:"300px"}},null,512)]),_:1})]),_:1}),t(d,{span:12},{default:e(()=>[t(c,null,{header:e(()=>[Ot]),default:e(()=>[s("div",{ref_key:"statusChartRef",ref:T,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"chart-container"},{default:e(()=>[t(d,{span:24},{default:e(()=>[t(c,null,{header:e(()=>[s("div",Dt,[Nt,t(X,null,{default:e(()=>[t(S,{size:"small",onClick:n[0]||(n[0]=v=>x(7)),type:o(h)===7?"primary":""},{default:e(()=>[O("7天")]),_:1},8,["type"]),t(S,{size:"small",onClick:n[1]||(n[1]=v=>x(15)),type:o(h)===15?"primary":""},{default:e(()=>[O("15天")]),_:1},8,["type"]),t(S,{size:"small",onClick:n[2]||(n[2]=v=>x(30)),type:o(h)===30?"primary":""},{default:e(()=>[O("30天")]),_:1},8,["type"])]),_:1})])]),default:e(()=>[s("div",{ref_key:"trendChartRef",ref:E,style:{height:"400px"}},null,512)]),_:1})]),_:1})]),_:1}),t(y,{class:"chart-container"},{default:e(()=>[t(d,{span:24},{default:e(()=>[t(c,null,{header:e(()=>[Rt]),default:e(()=>[t(q,{data:o(N),style:{width:"100%"}},{default:e(()=>[t(k,{prop:"warehouseName",label:"场库名称"}),t(k,{prop:"count",label:"异常订单数量",sortable:""}),t(k,{label:"占比"},{default:e(v=>[t(j,{percentage:I(v.row.count),"stroke-width":8,"show-text":!0,format:()=>I(v.row.count)+"%"},null,8,["percentage","format"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}}),Mt=G(Et,[["__scopeId","data-v-d4f82b7d"]]);export{Mt as default};
