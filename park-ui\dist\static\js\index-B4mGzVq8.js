import{M as me,g as ge,r as m,A as _e,R as ye,d as i,O as E,c as R,o as d,P as _,e,Q as ve,k as a,f as o,l as F,J as Q,K as L,i as g,N as D,q as s,h as M,t as A,a5 as be,a6 as he,a7 as Ve,a8 as we,a9 as ke,aa as Ce}from"./index-kDFdMZgN.js";import{C as O}from"./index-D3uM1NSj.js";const xe={class:"app-container"},Ne={class:"dialog-footer"},Se=me({name:"Config"}),Re=Object.assign(Se,{components:{CustomPagination:O}},{setup(Te){const{proxy:c}=ge(),{sys_yes_no:N}=c.useDict("sys_yes_no"),P=m([]),y=m(!1),S=m(!0),C=m(!0),T=m([]),$=m(!0),q=m(!0),I=m(0),K=m(""),w=m([]),j=_e({form:{},queryParams:{pageNum:1,pageSize:10,configName:void 0,configKey:void 0,configType:void 0},rules:{configName:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],configKey:[{required:!0,message:"参数键名不能为空",trigger:"blur"}],configValue:[{required:!0,message:"参数键值不能为空",trigger:"blur"}]}}),{queryParams:u,form:t,rules:J}=ye(j);function h(){S.value=!0,be(c.addDateRange(u.value,w.value)).then(r=>{P.value=r.rows,I.value=r.total,S.value=!1})}function G(){y.value=!1,U()}function U(){t.value={configId:void 0,configName:void 0,configKey:void 0,configValue:void 0,configType:"Y",remark:void 0},c.resetForm("configRef")}function x(){u.value.pageNum=1,h()}function H(){w.value=[],c.resetForm("queryRef"),x()}function W(r){T.value=r.map(n=>n.configId),$.value=r.length!=1,q.value=!r.length}function X(){U(),y.value=!0,K.value="添加参数"}function z(r){U();const n=r.configId||T.value;he(n).then(v=>{t.value=v.data,y.value=!0,K.value="修改参数"})}function Z(){c.$refs.configRef.validate(r=>{r&&(t.value.configId!=null?ke(t.value).then(n=>{c.$modal.msgSuccess("修改成功"),y.value=!1,h()}):Ce(t.value).then(n=>{c.$modal.msgSuccess("新增成功"),y.value=!1,h()}))})}function Y(r){const n=r.configId||T.value;c.$modal.confirm('是否确认删除参数编号为"'+n+'"的数据项？').then(function(){return Ve(n)}).then(()=>{h(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){c.download("system/config/export",{...u.value},`config_${new Date().getTime()}.xlsx`)}function le(){we().then(()=>{c.$modal.msgSuccess("刷新缓存成功")})}return h(),(r,n)=>{const v=i("el-input"),f=i("el-form-item"),oe=i("el-option"),ae=i("el-select"),ne=i("el-date-picker"),p=i("el-button"),B=i("el-form"),k=i("el-col"),te=i("right-toolbar"),ie=i("el-row"),b=i("el-table-column"),ue=i("dict-tag"),re=i("el-table"),de=i("el-radio"),se=i("el-radio-group"),ce=i("el-dialog"),V=E("hasPermi"),pe=E("loading");return d(),R("div",xe,[_(e(B,{model:a(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[e(f,{label:"参数名称",prop:"configName"},{default:o(()=>[e(v,{modelValue:a(u).configName,"onUpdate:modelValue":n[0]||(n[0]=l=>a(u).configName=l),placeholder:"请输入参数名称",clearable:"",style:{width:"240px"},onKeyup:F(x,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"参数键名",prop:"configKey"},{default:o(()=>[e(v,{modelValue:a(u).configKey,"onUpdate:modelValue":n[1]||(n[1]=l=>a(u).configKey=l),placeholder:"请输入参数键名",clearable:"",style:{width:"240px"},onKeyup:F(x,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"系统内置",prop:"configType"},{default:o(()=>[e(ae,{modelValue:a(u).configType,"onUpdate:modelValue":n[2]||(n[2]=l=>a(u).configType=l),placeholder:"系统内置",clearable:"",style:{width:"240px"}},{default:o(()=>[(d(!0),R(Q,null,L(a(N),l=>(d(),g(oe,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"创建时间",style:{width:"308px"}},{default:o(()=>[e(ne,{modelValue:a(w),"onUpdate:modelValue":n[3]||(n[3]=l=>D(w)?w.value=l:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(f,null,{default:o(()=>[e(p,{type:"primary",icon:"Search",onClick:x},{default:o(()=>[s("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:H},{default:o(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,a(C)]]),e(ie,{gutter:10,class:"mb8"},{default:o(()=>[e(k,{span:1.5},{default:o(()=>[_((d(),g(p,{type:"primary",plain:"",icon:"Plus",onClick:X},{default:o(()=>[s("新增")]),_:1})),[[V,["system:config:add"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[_((d(),g(p,{type:"success",plain:"",icon:"Edit",disabled:a($),onClick:z},{default:o(()=>[s("修改")]),_:1},8,["disabled"])),[[V,["system:config:edit"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[_((d(),g(p,{type:"danger",plain:"",icon:"Delete",disabled:a(q),onClick:Y},{default:o(()=>[s("删除")]),_:1},8,["disabled"])),[[V,["system:config:remove"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[_((d(),g(p,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:o(()=>[s("导出")]),_:1})),[[V,["system:config:export"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[_((d(),g(p,{type:"danger",plain:"",icon:"Refresh",onClick:le},{default:o(()=>[s("刷新缓存")]),_:1})),[[V,["system:config:remove"]]])]),_:1}),e(te,{showSearch:a(C),"onUpdate:showSearch":n[4]||(n[4]=l=>D(C)?C.value=l:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),_((d(),g(re,{data:a(P),onSelectionChange:W},{default:o(()=>[e(b,{type:"selection",width:"55",align:"center"}),e(b,{label:"参数名称",align:"center",prop:"configName","show-overflow-tooltip":!0}),e(b,{label:"参数键名",align:"center",prop:"configKey","show-overflow-tooltip":!0}),e(b,{label:"参数键值",align:"center",prop:"configValue","show-overflow-tooltip":!0}),e(b,{label:"系统内置",align:"center",prop:"configType"},{default:o(l=>[e(ue,{options:a(N),value:l.row.configType},null,8,["options","value"])]),_:1}),e(b,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),e(b,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:o(l=>[M("span",null,A(r.parseTime(l.row.createTime)),1)]),_:1}),e(b,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:o(l=>[_((d(),g(p,{link:"",type:"primary",icon:"Edit",onClick:fe=>z(l.row)},{default:o(()=>[s("修改")]),_:2},1032,["onClick"])),[[V,["system:config:edit"]]]),_((d(),g(p,{link:"",type:"primary",icon:"Delete",onClick:fe=>Y(l.row)},{default:o(()=>[s("删除")]),_:2},1032,["onClick"])),[[V,["system:config:remove"]]])]),_:1})]),_:1},8,["data"])),[[pe,a(S)]]),e(O,{total:a(I),"current-page":a(u).pageNum,"onUpdate:currentPage":n[5]||(n[5]=l=>a(u).pageNum=l),"page-size":a(u).pageSize,"onUpdate:pageSize":n[6]||(n[6]=l=>a(u).pageSize=l),onPagination:h},null,8,["total","current-page","page-size"]),e(ce,{title:a(K),modelValue:a(y),"onUpdate:modelValue":n[12]||(n[12]=l=>D(y)?y.value=l:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[M("div",Ne,[e(p,{type:"primary",onClick:Z},{default:o(()=>[s("确 定")]),_:1}),e(p,{onClick:G},{default:o(()=>[s("取 消")]),_:1})])]),default:o(()=>[e(B,{ref:"configRef",model:a(t),rules:a(J),"label-width":"80px"},{default:o(()=>[e(f,{label:"参数名称",prop:"configName"},{default:o(()=>[e(v,{modelValue:a(t).configName,"onUpdate:modelValue":n[7]||(n[7]=l=>a(t).configName=l),placeholder:"请输入参数名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"参数键名",prop:"configKey"},{default:o(()=>[e(v,{modelValue:a(t).configKey,"onUpdate:modelValue":n[8]||(n[8]=l=>a(t).configKey=l),placeholder:"请输入参数键名"},null,8,["modelValue"])]),_:1}),e(f,{label:"参数键值",prop:"configValue"},{default:o(()=>[e(v,{modelValue:a(t).configValue,"onUpdate:modelValue":n[9]||(n[9]=l=>a(t).configValue=l),type:"textarea",placeholder:"请输入参数键值"},null,8,["modelValue"])]),_:1}),e(f,{label:"系统内置",prop:"configType"},{default:o(()=>[e(se,{modelValue:a(t).configType,"onUpdate:modelValue":n[10]||(n[10]=l=>a(t).configType=l)},{default:o(()=>[(d(!0),R(Q,null,L(a(N),l=>(d(),g(de,{key:l.value,value:l.value},{default:o(()=>[s(A(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"备注",prop:"remark"},{default:o(()=>[e(v,{modelValue:a(t).remark,"onUpdate:modelValue":n[11]||(n[11]=l=>a(t).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Re as default};
