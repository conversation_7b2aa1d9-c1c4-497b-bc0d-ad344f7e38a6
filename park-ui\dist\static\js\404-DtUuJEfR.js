import{_,v as i,d,c as l,h as s,x as n,e as r,t as p,k as h,f as u,q as m,y as v,z as b,o as g}from"./index-kDFdMZgN.js";const f="/static/png/404-N4aRkdWY.png",a="/static/png/404_cloud-CPexjtDj.png",c=t=>(v("data-v-5945313b"),t=t(),b(),t),x={class:"wscn-http404-container"},k={class:"wscn-http404"},N=n('<div class="pic-404" data-v-5945313b><img class="pic-404__parent" src="'+f+'" alt="404" data-v-5945313b><img class="pic-404__child left" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child right" src="'+a+'" alt="404" data-v-5945313b></div>',1),S={class:"bullshit"},w=c(()=>s("div",{class:"bullshit__oops"}," 404错误! ",-1)),I={class:"bullshit__headline"},V=c(()=>s("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),B={__name:"404",setup(t){let e=i(()=>"找不到网页！");return(C,j)=>{const o=d("router-link");return g(),l("div",x,[s("div",k,[N,s("div",S,[w,s("div",I,p(h(e)),1),V,r(o,{to:"/index",class:"bullshit__return-home"},{default:u(()=>[m(" 返回首页 ")]),_:1})])])])}}},D=_(B,[["__scopeId","data-v-5945313b"]]);export{D as default};
