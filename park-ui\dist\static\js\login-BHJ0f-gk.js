import{_ as E,b as R,u as I,a as K,g as N,r as c,w as S,d,c as h,e as o,f as n,h as y,i as j,j as D,t as F,k as e,l as b,o as _,m as L,n as M}from"./index-kDFdMZgN.js";const O={class:"login"},P={class:"title"},Q={class:"login-code"},$=["src"],A={key:0},G={key:1},H={__name:"login",setup(J){const k="捷运停车管理系统",x=R(),v=I(),C=K(),{proxy:q}=N(),s=c({username:"",password:"",code:"",uuid:""}),U={username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},w=c(""),p=c(!1),m=c(!0),V=c(void 0);S(v,t=>{V.value=t.query&&t.query.redirect},{immediate:!0});function g(){q.$refs.loginRef.validate(t=>{t&&(p.value=!0,x.login(s.value).then(()=>{const a=v.query,i=Object.keys(a).reduce((r,l)=>(l!=="redirect"&&(r[l]=a[l]),r),{});C.push({path:V.value||"/",query:i})}).catch(()=>{p.value=!1,m.value&&f()}))})}function f(){M().then(t=>{m.value=t.captchaEnabled===void 0?!0:t.captchaEnabled,m.value&&(w.value="data:image/gif;base64,"+t.img,s.value.uuid=t.uuid)})}return f(),(t,a)=>{const i=d("svg-icon"),r=d("el-input"),l=d("el-form-item"),z=d("el-button"),B=d("el-form");return _(),h("div",O,[o(B,{ref:"loginRef",model:e(s),rules:U,class:"login-form"},{default:n(()=>[y("h3",P,F(e(k)),1),o(l,{prop:"username"},{default:n(()=>[o(r,{modelValue:e(s).username,"onUpdate:modelValue":a[0]||(a[0]=u=>e(s).username=u),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:n(()=>[o(i,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),o(l,{prop:"password"},{default:n(()=>[o(r,{modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=u=>e(s).password=u),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:b(g,["enter"])},{prefix:n(()=>[o(i,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(m)?(_(),j(l,{key:0,prop:"code"},{default:n(()=>[o(r,{modelValue:e(s).code,"onUpdate:modelValue":a[2]||(a[2]=u=>e(s).code=u),size:"large","auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:b(g,["enter"])},{prefix:n(()=>[o(i,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),y("div",Q,[y("img",{src:e(w),onClick:f,class:"login-code-img"},null,8,$)])]),_:1})):D("",!0),o(l,{style:{width:"100%"}},{default:n(()=>[o(z,{loading:e(p),size:"large",type:"primary",style:{width:"100%"},onClick:L(g,["prevent"])},{default:n(()=>[e(p)?(_(),h("span",G,"登 录 中...")):(_(),h("span",A,"登 录"))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])}}},W=E(H,[["__scopeId","data-v-6040a060"]]);export{W as default};
