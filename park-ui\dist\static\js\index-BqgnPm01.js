import{s as B,M as K,g as O,r as d,d as c,O as C,c as F,o as y,e as t,P as x,f as l,l as V,k as e,q as b,i as P,h as L,t as S,N as q}from"./index-kDFdMZgN.js";import{C as z}from"./index-D3uM1NSj.js";function Q(p){return B({url:"/system/online/list",method:"get",params:p})}function j(p){return B({url:"/system/online/"+p,method:"delete"})}const E={class:"app-container"},M=K({name:"Online"}),J=Object.assign(M,{components:{CustomPagination:z}},{setup(p){const{proxy:_}=O(),w=d([]),g=d(!0),k=d(0),a=d(1),i=d(10),s=d({ipaddr:void 0,userName:void 0});function m(){g.value=!0,Q(s.value).then(r=>{w.value=r.rows,k.value=r.total,g.value=!1})}function f(){a.value=1,m()}function D(){_.resetForm("queryRef"),f()}function R(r){_.$modal.confirm('是否确认强退名称为"'+r.userName+'"的用户?').then(function(){return j(r.tokenId)}).then(()=>{m(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}return m(),(r,o)=>{const N=c("el-input"),h=c("el-form-item"),v=c("el-button"),T=c("el-form"),u=c("el-table-column"),U=c("el-table"),$=C("hasPermi"),I=C("loading");return y(),F("div",E,[t(T,{model:e(s),ref:"queryRef",inline:!0},{default:l(()=>[t(h,{label:"登录地址",prop:"ipaddr"},{default:l(()=>[t(N,{modelValue:e(s).ipaddr,"onUpdate:modelValue":o[0]||(o[0]=n=>e(s).ipaddr=n),placeholder:"请输入登录地址",clearable:"",style:{width:"200px"},onKeyup:V(f,["enter"])},null,8,["modelValue"])]),_:1}),t(h,{label:"用户名称",prop:"userName"},{default:l(()=>[t(N,{modelValue:e(s).userName,"onUpdate:modelValue":o[1]||(o[1]=n=>e(s).userName=n),placeholder:"请输入用户名称",clearable:"",style:{width:"200px"},onKeyup:V(f,["enter"])},null,8,["modelValue"])]),_:1}),t(h,null,{default:l(()=>[t(v,{type:"primary",icon:"Search",onClick:f},{default:l(()=>[b("搜索")]),_:1}),t(v,{icon:"Refresh",onClick:D},{default:l(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),x((y(),P(U,{data:e(w).slice((e(a)-1)*e(i),e(a)*e(i)),style:{width:"100%"}},{default:l(()=>[t(u,{label:"序号",width:"50",type:"index",align:"center"},{default:l(n=>[L("span",null,S((e(a)-1)*e(i)+n.$index+1),1)]),_:1}),t(u,{label:"会话编号",align:"center",prop:"tokenId","show-overflow-tooltip":!0}),t(u,{label:"登录名称",align:"center",prop:"userName","show-overflow-tooltip":!0}),t(u,{label:"主机",align:"center",prop:"ipaddr","show-overflow-tooltip":!0}),t(u,{label:"登录时间",align:"center",prop:"loginTime",width:"180"},{default:l(n=>[L("span",null,S(r.parseTime(n.row.loginTime)),1)]),_:1}),t(u,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(n=>[x((y(),P(v,{link:"",type:"primary",icon:"Delete",onClick:A=>R(n.row)},{default:l(()=>[b("强退")]),_:2},1032,["onClick"])),[[$,["monitor:online:forceLogout"]]])]),_:1})]),_:1},8,["data"])),[[I,e(g)]]),t(z,{total:e(k),"current-page":e(a),"onUpdate:currentPage":o[2]||(o[2]=n=>q(a)?a.value=n:null),"page-size":e(i),"onUpdate:pageSize":o[3]||(o[3]=n=>q(i)?i.value=n:null),onPagination:m},null,8,["total","current-page","page-size"])])}}});export{J as default};
