import{s as j,_ as re,M as ue,g as de,r as f,A as se,R as ie,p as ce,d as a,O as Q,c as P,o as i,P as g,e as t,Q as U,f as e,l as pe,k as l,q as o,i as m,h,t as d,Z as B,j as z}from"./index-kDFdMZgN.js";function _e(v){return j({url:"/system/refund/list",method:"get",params:v})}function fe(v){return j({url:"/system/refund/"+v,method:"get"})}const me={class:"app-container"},he={key:2},ye={class:"amount-text"},ge={class:"refund-amount-text"},ve={key:2},we={class:"amount-text"},be={class:"refund-amount-text"},xe={class:"dialog-footer"},ke=ue({name:"Refund"}),Te=Object.assign(ke,{setup(v){const{proxy:k}=de(),N=f([]),w=f(!1),T=f(!0),V=f(!0),E=f([]),F=f(!0),K=f(!0),C=f(0),L=f(""),M=se({form:{},queryParams:{pageNum:1,pageSize:10,tradeId:null,refundType:null}}),{queryParams:s,form:r}=ie(M);function O(){w.value=!1,A()}function A(){r.value={},k.resetForm("refundRef")}function I(){s.value.pageNum=1,b()}function $(){k.resetForm("queryRef"),I()}function Z(c){E.value=c.map(u=>u.id),F.value=c.length!=1,K.value=!c.length}function G(c){A();const u=c.id;fe(u).then(R=>{r.value=R.data,w.value=!0,L.value="查看退款详情"})}function H(){k.download("refund/export",{...s.value},`退款记录_${new Date().getTime()}.xlsx`)}function b(c){c&&(s.value.pageNum=c.page,s.value.pageSize=c.limit),T.value=!0,_e(s.value).then(u=>{N.value=u.rows,C.value=u.total,T.value=!1})}return ce(()=>{b()}),(c,u)=>{const R=a("el-input"),S=a("el-form-item"),D=a("el-option"),J=a("el-select"),y=a("el-button"),W=a("el-form"),X=a("el-col"),Y=a("right-toolbar"),ee=a("el-row"),p=a("el-table-column"),x=a("el-tag"),te=a("el-table"),le=a("pagination"),_=a("el-descriptions-item"),ne=a("el-descriptions"),ae=a("el-dialog"),q=Q("hasPermi"),oe=Q("loading");return i(),P("div",me,[g(t(W,{model:l(s),ref:"queryRef",inline:!0,"label-width":"80px"},{default:e(()=>[t(S,{label:"订单号",prop:"tradeId"},{default:e(()=>[t(R,{modelValue:l(s).tradeId,"onUpdate:modelValue":u[0]||(u[0]=n=>l(s).tradeId=n),placeholder:"请输入订单号",clearable:"",style:{width:"200px"},onKeyup:pe(I,["enter"])},null,8,["modelValue"])]),_:1}),t(S,{label:"退款类型",prop:"refundType"},{default:e(()=>[t(J,{modelValue:l(s).refundType,"onUpdate:modelValue":u[1]||(u[1]=n=>l(s).refundType=n),placeholder:"请选择退款类型",clearable:"",style:{width:"150px"}},{default:e(()=>[t(D,{label:"临停订单",value:"1"}),t(D,{label:"VIP会员",value:"2"})]),_:1},8,["modelValue"])]),_:1}),t(S,null,{default:e(()=>[t(y,{type:"primary",icon:"Search",onClick:I},{default:e(()=>[o("搜索")]),_:1}),t(y,{icon:"Refresh",onClick:$},{default:e(()=>[o("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[U,V.value]]),t(ee,{gutter:10,class:"mb8"},{default:e(()=>[t(X,{span:1.5},{default:e(()=>[g((i(),m(y,{type:"warning",plain:"",icon:"Download",onClick:H},{default:e(()=>[o("导出")]),_:1})),[[q,["refund:refund:export"]]])]),_:1}),t(Y,{"show-search":V.value,"onUpdate:showSearch":u[2]||(u[2]=n=>V.value=n),onQueryTable:b},null,8,["show-search"])]),_:1}),g((i(),m(te,{data:N.value,onSelectionChange:Z},{default:e(()=>[t(p,{type:"selection",width:"55",align:"center"}),t(p,{label:"退款ID",align:"center",prop:"id","min-width":"80"}),t(p,{label:"订单号",align:"center",prop:"tradeId","min-width":"180","show-overflow-tooltip":""}),t(p,{label:"退款类型",align:"center",prop:"refundType","min-width":"100"},{default:e(n=>[n.row.refundType===1?(i(),m(x,{key:0,type:"primary"},{default:e(()=>[o("临停订单")]),_:1})):n.row.refundType===2?(i(),m(x,{key:1,type:"success"},{default:e(()=>[o("VIP会员")]),_:1})):(i(),P("span",he,"--"))]),_:1}),t(p,{label:"原订单金额",align:"center",prop:"originalAmount","min-width":"120"},{default:e(n=>[h("span",ye,"¥"+d(n.row.originalAmount),1)]),_:1}),t(p,{label:"退款金额",align:"center",prop:"refundAmount","min-width":"120"},{default:e(n=>[h("span",ge,"¥"+d(n.row.refundAmount),1)]),_:1}),t(p,{label:"退款原因",align:"center",prop:"refundReason","min-width":"200","show-overflow-tooltip":""},{default:e(n=>[h("span",null,d(n.row.refundReason||"--"),1)]),_:1}),t(p,{label:"创建者",align:"center",prop:"createBy","min-width":"100"}),t(p,{label:"创建时间",align:"center",prop:"createTime","min-width":"160","show-overflow-tooltip":""},{default:e(n=>[h("span",null,d(l(B)(n.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(p,{label:"操作",align:"center",width:"100",fixed:"right"},{default:e(n=>[g((i(),m(y,{link:"",type:"primary",icon:"View",onClick:Ve=>G(n.row)},{default:e(()=>[o("查看")]),_:2},1032,["onClick"])),[[q,["refund:refund:query"]]])]),_:1})]),_:1},8,["data"])),[[oe,T.value]]),g(t(le,{total:C.value,page:l(s).pageNum,limit:l(s).pageSize,onPagination:b},null,8,["total","page","limit"]),[[U,C.value>0]]),t(ae,{title:"退款详情",modelValue:w.value,"onUpdate:modelValue":u[3]||(u[3]=n=>w.value=n),width:"600px","append-to-body":""},{footer:e(()=>[h("div",xe,[t(y,{onClick:O},{default:e(()=>[o("关 闭")]),_:1})])]),default:e(()=>[t(ne,{column:2,border:""},{default:e(()=>[t(_,{label:"退款ID"},{default:e(()=>[o(d(l(r).id),1)]),_:1}),t(_,{label:"订单号"},{default:e(()=>[o(d(l(r).tradeId),1)]),_:1}),t(_,{label:"退款类型"},{default:e(()=>[l(r).refundType===1?(i(),m(x,{key:0,type:"primary"},{default:e(()=>[o("临停订单")]),_:1})):l(r).refundType===2?(i(),m(x,{key:1,type:"success"},{default:e(()=>[o("VIP会员")]),_:1})):(i(),P("span",ve,"--"))]),_:1}),t(_,{label:"原订单金额"},{default:e(()=>[h("span",we,"¥"+d(l(r).originalAmount),1)]),_:1}),t(_,{label:"退款金额"},{default:e(()=>[h("span",be,"¥"+d(l(r).refundAmount),1)]),_:1}),t(_,{label:"退款原因",span:2},{default:e(()=>[o(d(l(r).refundReason||"--"),1)]),_:1}),t(_,{label:"创建者"},{default:e(()=>[o(d(l(r).createBy),1)]),_:1}),t(_,{label:"创建时间"},{default:e(()=>[o(d(l(B)(l(r).createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(r).updateBy?(i(),m(_,{key:0,label:"更新者"},{default:e(()=>[o(d(l(r).updateBy),1)]),_:1})):z("",!0),l(r).updateTime?(i(),m(_,{key:1,label:"更新时间"},{default:e(()=>[o(d(l(B)(l(r).updateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})):z("",!0)]),_:1})]),_:1},8,["modelValue"])])}}}),Ie=re(Te,[["__scopeId","data-v-8150610c"]]);export{Ie as default};
