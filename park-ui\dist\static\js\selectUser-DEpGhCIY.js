import{M as A,g as D,r as f,A as Q,d as s,i as E,o as F,f as l,e,k as a,l as v,q as _,h as N,t as M,N as O}from"./index-kDFdMZgN.js";import{u as G,d as H}from"./role-D90S1TPl.js";import{C as S}from"./index-D3uM1NSj.js";const J={class:"dialog-footer"},W=A({name:"SelectUser"}),ee=Object.assign(W,{components:{CustomPagination:S}},{props:{roleId:{type:[Number,String]}},emits:["ok"],setup(C,{expose:k,emit:I}){const V=C,{proxy:p}=D(),{sys_normal_disable:x}=p.useDict("sys_normal_disable"),w=f([]),i=f(!1),h=f(0),y=f([]),n=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,phonenumber:void 0});function U(){n.roleId=V.roleId,g(),i.value=!0}function R(r){p.$refs.refTable.toggleRowSelection(r)}function z(r){y.value=r.map(o=>o.userId)}function g(){G(n).then(r=>{w.value=r.rows,h.value=r.total})}function d(){n.pageNum=1,g()}function T(){p.resetForm("queryRef"),d()}const q=I;function P(){const r=n.roleId,o=y.value.join(",");if(o==""){p.$modal.msgError("请选择要分配的用户");return}H({roleId:r,userIds:o}).then(m=>{p.$modal.msgSuccess(m.msg),i.value=!1,q("ok")})}return k({show:U}),(r,o)=>{const m=s("el-input"),b=s("el-form-item"),c=s("el-button"),$=s("el-form"),u=s("el-table-column"),B=s("dict-tag"),K=s("el-table"),L=s("el-row"),j=s("el-dialog");return F(),E(j,{title:"选择用户",modelValue:a(i),"onUpdate:modelValue":o[5]||(o[5]=t=>O(i)?i.value=t:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[N("div",J,[e(c,{type:"primary",onClick:P},{default:l(()=>[_("确 定")]),_:1}),e(c,{onClick:o[4]||(o[4]=t=>i.value=!1)},{default:l(()=>[_("取 消")]),_:1})])]),default:l(()=>[e($,{model:a(n),ref:"queryRef",inline:!0},{default:l(()=>[e(b,{label:"用户名称",prop:"userName"},{default:l(()=>[e(m,{modelValue:a(n).userName,"onUpdate:modelValue":o[0]||(o[0]=t=>a(n).userName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"180px"},onKeyup:v(d,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"手机号码",prop:"phonenumber"},{default:l(()=>[e(m,{modelValue:a(n).phonenumber,"onUpdate:modelValue":o[1]||(o[1]=t=>a(n).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"180px"},onKeyup:v(d,["enter"])},null,8,["modelValue"])]),_:1}),e(b,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:d},{default:l(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:T},{default:l(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(L,null,{default:l(()=>[e(K,{onRowClick:R,ref:"refTable",data:a(w),onSelectionChange:z,height:"260px"},{default:l(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),e(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),e(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),e(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),e(u,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(B,{options:a(x),value:t.row.status},null,8,["options","value"])]),_:1}),e(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(t=>[N("span",null,M(r.parseTime(t.row.createTime)),1)]),_:1})]),_:1},8,["data"]),e(S,{total:a(h),"current-page":a(n).pageNum,"onUpdate:currentPage":o[2]||(o[2]=t=>a(n).pageNum=t),"page-size":a(n).pageSize,"onUpdate:pageSize":o[3]||(o[3]=t=>a(n).pageSize=t),onPagination:g},null,8,["total","current-page","page-size"])]),_:1})]),_:1},8,["modelValue"])}}});export{ee as default};
