import{s as V,M as fe,g as ge,r as c,A as ye,R as _e,p as he,d,O as j,c as z,o as p,P as b,e,Q as ve,k as t,f as n,J as A,K as M,i as _,l as J,q as y,N as G,j as be,h as H,t as Ce}from"./index-kDFdMZgN.js";import{g as we}from"./member-Bwl49t4m.js";import{C as Pe}from"./index-D3uM1NSj.js";function Ve(s){return V({url:"/system/platform/unionPayConfig/list",method:"get",params:s})}function ke(s){return V({url:"/system/platform/unionPayConfig/"+s,method:"get"})}function xe(s){return V({url:"/system/platform/unionPayConfig",method:"post",data:s})}function Ue(s){return V({url:"/system/platform/unionPayConfig",method:"put",data:s})}function Te(s){return V({url:"/system/platform/unionPayConfig/"+s,method:"delete"})}function Se(s,m){return V({url:"/system/platform/unionPayConfig/checkTidUnique",method:"get",params:{tid:s,id:m}})}const Ie={class:"app-container"},Ne={class:"dialog-footer"},qe=fe({name:"UnionPayConfig"}),Oe=Object.assign(qe,{setup(s){const{proxy:m}=ge(),{pay_type:I}=m.useDict("pay_type"),B=c([]),X=c([]),N=c([]),h=c(!1),q=c(!0),U=c(!0),D=c([]),E=c(!0),F=c(!0),K=c(0),R=c(""),Y=ye({form:{},queryParams:{pageNum:1,pageSize:10,warehouseId:null,payType:null,mid:null,tid:null},rules:{warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],payType:[{required:!0,message:"支付类型不能为空",trigger:"change"}],mid:[{required:!0,message:"商户号不能为空",trigger:"blur"},{min:1,max:32,message:"商户号长度必须介于 1 和 32 之间",trigger:"blur"}],tid:[{required:!0,message:"终端号不能为空",trigger:"blur"},{min:1,max:32,message:"终端号长度必须介于 1 和 32 之间",trigger:"blur"},{validator:(o,l,f)=>{l?Se(l,i.id).then(r=>{r.data?f():f(new Error("终端号已存在"))}):f()},trigger:"blur"}]}}),{queryParams:u,form:i,rules:Z}=_e(Y);function C(){q.value=!0,Ve(u.value).then(o=>{B.value=o.rows,K.value=o.total,q.value=!1})}function ee(){h.value=!1,$()}function $(){i.value={id:null,warehouseId:null,payType:null,mid:null,tid:null,remark:null},m.resetForm("unionPayConfigRef")}function T(){u.value.pageNum=1,C()}function le(){m.resetForm("queryRef"),T()}function ae(o){D.value=o.map(l=>l.id),E.value=o.length!=1,F.value=!o.length}function te(){$(),O(),h.value=!0,R.value="添加银联配置"}function L(o){$(),O();const l=o.id||D.value;ke(l).then(f=>{i.value=f.data,h.value=!0,R.value="修改银联配置"})}function ne(){m.$refs.unionPayConfigRef.validate(o=>{o&&(i.value.id!=null?Ue(i.value).then(l=>{m.$modal.msgSuccess("修改成功"),h.value=!1,C()}):xe(i.value).then(l=>{m.$modal.msgSuccess("新增成功"),h.value=!1,C()}))})}function Q(o){const l=o.id||D.value;m.$modal.confirm('是否确认删除银联配置编号为"'+l+'"的数据项？').then(function(){return Te(l)}).then(()=>{C(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){m.download("system/platform/unionPayConfig/export",{...u.value},`unionPayConfig_${new Date().getTime()}.xlsx`)}function O(){we().then(o=>{X.value=o.data,N.value=re(o.data)})}function re(o){if(!o||o.length===0)return[];const l=o.filter(r=>r.parentId==="0"),f=o.filter(r=>r.parentId!=="0");return l.map(r=>{const k=f.filter(w=>w.parentId===r.id).map(w=>({value:w.id,label:w.warehouseName,isLeaf:!0}));return{value:r.id,label:r.warehouseName,children:k.length>0?k:void 0}})}return he(()=>{O(),C()}),(o,l)=>{const f=d("el-cascader"),r=d("el-form-item"),k=d("el-option"),w=d("el-select"),x=d("el-input"),g=d("el-button"),W=d("el-form"),S=d("el-col"),ie=d("right-toolbar"),ue=d("el-row"),v=d("el-table-column"),de=d("dict-tag"),se=d("el-table"),pe=d("el-dialog"),P=j("hasPermi"),me=j("loading");return p(),z("div",Ie,[b(e(W,{model:t(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[e(r,{label:"场库",prop:"warehouseId"},{default:n(()=>[e(f,{modelValue:t(u).warehouseId,"onUpdate:modelValue":l[0]||(l[0]=a=>t(u).warehouseId=a),options:t(N),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(r,{label:"支付类型",prop:"payType"},{default:n(()=>[e(w,{modelValue:t(u).payType,"onUpdate:modelValue":l[1]||(l[1]=a=>t(u).payType=a),placeholder:"请选择支付类型",clearable:"",style:{width:"200px"}},{default:n(()=>[(p(!0),z(A,null,M(t(I),a=>(p(),_(k,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"商户号",prop:"mid"},{default:n(()=>[e(x,{modelValue:t(u).mid,"onUpdate:modelValue":l[2]||(l[2]=a=>t(u).mid=a),placeholder:"请输入商户号",clearable:"",style:{width:"200px"},onKeyup:J(T,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"终端号",prop:"tid"},{default:n(()=>[e(x,{modelValue:t(u).tid,"onUpdate:modelValue":l[3]||(l[3]=a=>t(u).tid=a),placeholder:"请输入终端号",clearable:"",style:{width:"200px"},onKeyup:J(T,["enter"])},null,8,["modelValue"])]),_:1}),e(r,null,{default:n(()=>[e(g,{type:"primary",icon:"Search",onClick:T},{default:n(()=>[y("搜索")]),_:1}),e(g,{icon:"Refresh",onClick:le},{default:n(()=>[y("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,t(U)]]),e(ue,{gutter:10,class:"mb8"},{default:n(()=>[e(S,{span:1.5},{default:n(()=>[b((p(),_(g,{type:"primary",plain:"",icon:"Plus",onClick:te},{default:n(()=>[y("新增")]),_:1})),[[P,["platform:unionPayConfig:add"]]])]),_:1}),e(S,{span:1.5},{default:n(()=>[b((p(),_(g,{type:"success",plain:"",icon:"Edit",disabled:t(E),onClick:L},{default:n(()=>[y("修改")]),_:1},8,["disabled"])),[[P,["platform:unionPayConfig:edit"]]])]),_:1}),e(S,{span:1.5},{default:n(()=>[b((p(),_(g,{type:"danger",plain:"",icon:"Delete",disabled:t(F),onClick:Q},{default:n(()=>[y("删除")]),_:1},8,["disabled"])),[[P,["platform:unionPayConfig:remove"]]])]),_:1}),e(S,{span:1.5},{default:n(()=>[b((p(),_(g,{type:"warning",plain:"",icon:"Download",onClick:oe},{default:n(()=>[y("导出")]),_:1})),[[P,["platform:unionPayConfig:export"]]])]),_:1}),e(ie,{showSearch:t(U),"onUpdate:showSearch":l[4]||(l[4]=a=>G(U)?U.value=a:null),onQueryTable:C},null,8,["showSearch"])]),_:1}),b((p(),_(se,{data:t(B),onSelectionChange:ae},{default:n(()=>[e(v,{type:"selection",width:"55",align:"center"}),be("",!0),e(v,{label:"场库名称",align:"center",prop:"warehouseName"}),e(v,{label:"商户号",align:"center",prop:"mid"}),e(v,{label:"终端号",align:"center",prop:"tid"}),e(v,{label:"支付类型",align:"center",prop:"payType"},{default:n(a=>[e(de,{options:t(I),value:a.row.payType},null,8,["options","value"])]),_:1}),e(v,{label:"备注",align:"center",prop:"remark"}),e(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:n(a=>[H("span",null,Ce(o.parseTime(a.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(v,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(a=>[b((p(),_(g,{link:"",type:"primary",icon:"Edit",onClick:ce=>L(a.row)},{default:n(()=>[y("修改")]),_:2},1032,["onClick"])),[[P,["platform:unionPayConfig:edit"]]]),b((p(),_(g,{link:"",type:"primary",icon:"Delete",onClick:ce=>Q(a.row)},{default:n(()=>[y("删除")]),_:2},1032,["onClick"])),[[P,["platform:unionPayConfig:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,t(q)]]),e(Pe,{total:t(K),"current-page":t(u).pageNum,"onUpdate:currentPage":l[5]||(l[5]=a=>t(u).pageNum=a),"page-size":t(u).pageSize,"onUpdate:pageSize":l[6]||(l[6]=a=>t(u).pageSize=a),onPagination:C},null,8,["total","current-page","page-size"]),e(pe,{title:t(R),modelValue:t(h),"onUpdate:modelValue":l[12]||(l[12]=a=>G(h)?h.value=a:null),width:"500px","append-to-body":""},{footer:n(()=>[H("div",Ne,[e(g,{type:"primary",onClick:ne},{default:n(()=>[y("确 定")]),_:1}),e(g,{onClick:ee},{default:n(()=>[y("取 消")]),_:1})])]),default:n(()=>[e(W,{ref:"unionPayConfigRef",model:t(i),rules:t(Z),"label-width":"80px"},{default:n(()=>[e(r,{label:"场库",prop:"warehouseId"},{default:n(()=>[e(f,{modelValue:t(i).warehouseId,"onUpdate:modelValue":l[7]||(l[7]=a=>t(i).warehouseId=a),options:t(N),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(r,{label:"支付类型",prop:"payType"},{default:n(()=>[e(w,{modelValue:t(i).payType,"onUpdate:modelValue":l[8]||(l[8]=a=>t(i).payType=a),placeholder:"请选择支付类型",style:{width:"100%"}},{default:n(()=>[(p(!0),z(A,null,M(t(I),a=>(p(),_(k,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"商户号",prop:"mid"},{default:n(()=>[e(x,{modelValue:t(i).mid,"onUpdate:modelValue":l[9]||(l[9]=a=>t(i).mid=a),placeholder:"请输入商户号"},null,8,["modelValue"])]),_:1}),e(r,{label:"终端号",prop:"tid"},{default:n(()=>[e(x,{modelValue:t(i).tid,"onUpdate:modelValue":l[10]||(l[10]=a=>t(i).tid=a),placeholder:"请输入终端号"},null,8,["modelValue"])]),_:1}),e(r,{label:"备注",prop:"remark"},{default:n(()=>[e(x,{modelValue:t(i).remark,"onUpdate:modelValue":l[11]||(l[11]=a=>t(i).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Oe as default};
