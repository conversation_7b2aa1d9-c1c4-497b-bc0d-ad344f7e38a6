import{a as D,u as O}from"./user-CzrZiQIy.js";import{C as y}from"./index-D3uM1NSj.js";import{M as E,u as K,g as q,r as u,B as F,d as s,O as M,c as G,o as C,h as m,e as t,P as H,f as a,k as l,i as J,t as S,N as V,q as $}from"./index-kDFdMZgN.js";const L={class:"app-container"},Q=m("h4",{class:"form-header h4"},"基本信息",-1),W=m("h4",{class:"form-header h4"},"角色信息",-1),X={style:{"text-align":"center","margin-left":"-120px","margin-top":"30px"}},Y=E({name:"AuthRole"}),le=Object.assign(Y,{components:{CustomPagination:y}},{setup(Z){const b=K(),{proxy:f}=q(),g=u(!0),v=u(0),i=u(1),c=u(10),h=u([]),_=u([]),r=u({nickName:void 0,userName:void 0,userId:void 0});function B(o){k(o)&&f.$refs.roleRef.toggleRowSelection(o)}function T(o){h.value=o.map(e=>e.roleId)}function z(o){return o.roleId}function k(o){return o.status==="0"}function w(){const o={path:"/system/user"};f.$tab.closeOpenPage(o)}function P(){const o=r.value.userId,e=h.value.join(",");O({userId:o,roleIds:e}).then(d=>{f.$modal.msgSuccess("授权成功"),w()})}return(()=>{const o=b.params&&b.params.userId;o&&(g.value=!0,D(o).then(e=>{r.value=e.user,_.value=e.roles,v.value=_.value.length,F(()=>{_.value.forEach(d=>{d.flag&&f.$refs.roleRef.toggleRowSelection(d)})}),g.value=!1}))})(),(o,e)=>{const d=s("el-input"),N=s("el-form-item"),I=s("el-col"),U=s("el-row"),R=s("el-form"),p=s("el-table-column"),j=s("el-table"),x=s("el-button"),A=M("loading");return C(),G("div",L,[Q,t(R,{model:l(r),"label-width":"80px"},{default:a(()=>[t(U,null,{default:a(()=>[t(I,{span:8,offset:2},{default:a(()=>[t(N,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(d,{modelValue:l(r).nickName,"onUpdate:modelValue":e[0]||(e[0]=n=>l(r).nickName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(I,{span:8,offset:2},{default:a(()=>[t(N,{label:"登录账号",prop:"userName"},{default:a(()=>[t(d,{modelValue:l(r).userName,"onUpdate:modelValue":e[1]||(e[1]=n=>l(r).userName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),W,H((C(),J(j,{"row-key":z,onRowClick:B,ref:"roleRef",onSelectionChange:T,data:l(_).slice((l(i)-1)*l(c),l(i)*l(c))},{default:a(()=>[t(p,{label:"序号",width:"55",type:"index",align:"center"},{default:a(n=>[m("span",null,S((l(i)-1)*l(c)+n.$index+1),1)]),_:1}),t(p,{type:"selection","reserve-selection":!0,selectable:k,width:"55"}),t(p,{label:"角色编号",align:"center",prop:"roleId"}),t(p,{label:"角色名称",align:"center",prop:"roleName"}),t(p,{label:"权限字符",align:"center",prop:"roleKey"}),t(p,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(n=>[m("span",null,S(o.parseTime(n.row.createTime)),1)]),_:1})]),_:1},8,["data"])),[[A,l(g)]]),t(y,{total:l(v),"current-page":l(i),"onUpdate:currentPage":e[2]||(e[2]=n=>V(i)?i.value=n:null),"page-size":l(c),"onUpdate:pageSize":e[3]||(e[3]=n=>V(c)?c.value=n:null)},null,8,["total","current-page","page-size"]),t(R,{"label-width":"100px"},{default:a(()=>[m("div",X,[t(x,{type:"primary",onClick:e[4]||(e[4]=n=>P())},{default:a(()=>[$("提交")]),_:1}),t(x,{onClick:e[5]||(e[5]=n=>w())},{default:a(()=>[$("返回")]),_:1})])]),_:1})])}}});export{le as default};
