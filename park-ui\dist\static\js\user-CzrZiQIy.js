import{s as t,ac as u}from"./index-kDFdMZgN.js";function n(e){return t({url:"/system/user/list",method:"get",params:e})}function o(e){return t({url:"/system/user/"+u(e),method:"get"})}function d(e){return t({url:"/system/user",method:"post",data:e})}function m(e){return t({url:"/system/user",method:"put",data:e})}function l(e){return t({url:"/system/user/"+e,method:"delete"})}function p(e,r){return t({url:"/system/user/resetPwd",method:"put",data:{userId:e,password:r}})}function i(e,r){return t({url:"/system/user/changeStatus",method:"put",data:{userId:e,status:r}})}function c(){return t({url:"/system/user/profile",method:"get"})}function f(e){return t({url:"/system/user/profile",method:"put",data:e})}function h(e,r){return t({url:"/system/user/profile/updatePwd",method:"put",data:{oldPassword:e,newPassword:r}})}function y(e){return t({url:"/system/user/profile/avatar",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:e})}function g(e){return t({url:"/system/user/authRole/"+e,method:"get"})}function U(e){return t({url:"/system/user/authRole",method:"put",params:e})}function w(){return t({url:"/system/user/deptTree",method:"get"})}export{g as a,y as b,f as c,h as d,w as e,o as f,c as g,l as h,i,m as j,d as k,n as l,p as r,U as u};
