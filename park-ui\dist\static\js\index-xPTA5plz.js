import{_ as ee,M as ae,g as te,r as c,A as le,R as oe,d,O as E,c as ne,o as g,P as _,e,Q as re,k as t,f as l,l as U,q as m,i as v,N as K,h as ie}from"./index-kDFdMZgN.js";import{l as pe,g as ue,d as de,u as me,a as se}from"./operator-DClop3bI.js";import{C as Q}from"./index-D3uM1NSj.js";const ce={class:"app-container"},fe={class:"dialog-footer"},ye=ae({name:"Operator"}),ge=Object.assign(ye,{components:{CustomPagination:Q}},{setup(_e){const{proxy:s}=te(),$=c([]),f=c(!1),k=c(!0),N=c(!0),x=c([]),D=c(!0),O=c(!0),R=c(0),P=c(""),F=le({form:{},queryParams:{pageNum:1,pageSize:10,companyName:null,primaryContactName:null,primaryContactPhone:null},rules:{companyName:[{required:!0,message:"运营商公司名称不能为空",trigger:"blur"}],primaryContactName:[{required:!0,message:"主要联系人姓名不能为空",trigger:"blur"}],primaryContactPhone:[{required:!0,message:"主要联系人手机号不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:r,form:i,rules:I}=oe(F);function h(){k.value=!0,pe(r.value).then(p=>{$.value=p.rows,R.value=p.total,k.value=!1})}function T(){f.value=!1,S()}function S(){i.value={id:null,companyName:null,primaryContactName:null,primaryContactPhone:null,remark:null},s.resetForm("operatorRef")}function w(){r.value.pageNum=1,h()}function j(){s.resetForm("queryRef"),Object.assign(r.value,{pageNum:1,pageSize:10,companyName:null,primaryContactName:null,primaryContactPhone:null}),w()}function A(p){x.value=p.map(a=>a.id),D.value=p.length!=1,O.value=!p.length}function L(){S(),f.value=!0,P.value="添加运营商信息"}function q(p){S();const a=p.id||x.value;ue(a).then(n=>{i.value=n.data,f.value=!0,P.value="修改运营商信息"})}function M(){s.$refs.operatorRef.validate(p=>{if(p){const a={...i.value};try{const n=s.$store.state.user;n&&n.id&&(i.value.id||(a.createdBy=n.id),a.updatedBy=n.id)}catch(n){console.warn("获取用户ID失败:",n)}i.value.id!=null?me(a).then(n=>{s.$modal.msgSuccess("修改成功"),f.value=!1,h()}):se(a).then(n=>{s.$modal.msgSuccess("新增成功"),f.value=!1,h()})}})}function z(p){const a=p.id||x.value;s.$modal.confirm('是否确认删除运营商信息编号为"'+a+'"的数据项？').then(function(){return de(a)}).then(()=>{h(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}function G(){s.download("system/platform/operator/export",{...r.value},`operator_${new Date().getTime()}.xlsx`)}return h(),(p,a)=>{const n=d("el-input"),y=d("el-form-item"),u=d("el-button"),B=d("el-form"),V=d("el-col"),H=d("right-toolbar"),J=d("el-row"),C=d("el-table-column"),W=d("el-table"),X=d("el-dialog"),b=E("hasPermi"),Y=E("loading");return g(),ne("div",ce,[_(e(B,{model:t(r),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(y,{label:"公司名称",prop:"companyName"},{default:l(()=>[e(n,{modelValue:t(r).companyName,"onUpdate:modelValue":a[0]||(a[0]=o=>t(r).companyName=o),placeholder:"请输入运营商公司名称",clearable:"",style:{width:"200px"},onKeyup:U(w,["enter"])},null,8,["modelValue"])]),_:1}),e(y,{label:"联系人",prop:"primaryContactName"},{default:l(()=>[e(n,{modelValue:t(r).primaryContactName,"onUpdate:modelValue":a[1]||(a[1]=o=>t(r).primaryContactName=o),placeholder:"请输入主要联系人姓名",clearable:"",style:{width:"200px"},onKeyup:U(w,["enter"])},null,8,["modelValue"])]),_:1}),e(y,{label:"手机号",prop:"primaryContactPhone"},{default:l(()=>[e(n,{modelValue:t(r).primaryContactPhone,"onUpdate:modelValue":a[2]||(a[2]=o=>t(r).primaryContactPhone=o),placeholder:"请输入主要联系人手机号",clearable:"",style:{width:"200px"},onKeyup:U(w,["enter"])},null,8,["modelValue"])]),_:1}),e(y,null,{default:l(()=>[e(u,{type:"primary",icon:"Search",onClick:w},{default:l(()=>[m("搜索")]),_:1}),e(u,{icon:"Refresh",onClick:j},{default:l(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[re,t(N)]]),e(J,{gutter:10,class:"mb8"},{default:l(()=>[e(V,{span:1.5},{default:l(()=>[_((g(),v(u,{type:"primary",plain:"",icon:"Plus",onClick:L},{default:l(()=>[m("新增")]),_:1})),[[b,["platform:operator:add"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[_((g(),v(u,{type:"success",plain:"",icon:"Edit",disabled:t(D),onClick:q},{default:l(()=>[m("修改")]),_:1},8,["disabled"])),[[b,["platform:operator:edit"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[_((g(),v(u,{type:"danger",plain:"",icon:"Delete",disabled:t(O),onClick:z},{default:l(()=>[m("删除")]),_:1},8,["disabled"])),[[b,["platform:operator:remove"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[_((g(),v(u,{type:"warning",plain:"",icon:"Download",onClick:G},{default:l(()=>[m("导出")]),_:1})),[[b,["platform:operator:export"]]])]),_:1}),e(H,{showSearch:t(N),"onUpdate:showSearch":a[3]||(a[3]=o=>K(N)?N.value=o:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),_((g(),v(W,{data:t($),onSelectionChange:A},{default:l(()=>[e(C,{type:"selection",width:"55",align:"center"}),e(C,{label:"公司名称",align:"center",prop:"companyName","show-overflow-tooltip":!0}),e(C,{label:"主要联系人",align:"center",prop:"primaryContactName"}),e(C,{label:"联系手机号",align:"center",prop:"primaryContactPhone"}),e(C,{label:"备注",align:"center",prop:"remark",width:"500"}),e(C,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"150"},{default:l(o=>[_((g(),v(u,{link:"",type:"primary",icon:"Edit",onClick:Z=>q(o.row)},{default:l(()=>[m("修改")]),_:2},1032,["onClick"])),[[b,["platform:operator:edit"]]]),_((g(),v(u,{link:"",type:"primary",icon:"Delete",onClick:Z=>z(o.row)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])),[[b,["platform:operator:remove"]]])]),_:1})]),_:1},8,["data"])),[[Y,t(k)]]),e(Q,{total:t(R),"current-page":t(r).pageNum,"onUpdate:currentPage":a[4]||(a[4]=o=>t(r).pageNum=o),"page-size":t(r).pageSize,"onUpdate:pageSize":a[5]||(a[5]=o=>t(r).pageSize=o),onPagination:h},null,8,["total","current-page","page-size"]),e(X,{title:t(P),modelValue:t(f),"onUpdate:modelValue":a[10]||(a[10]=o=>K(f)?f.value=o:null),width:"500px","append-to-body":""},{footer:l(()=>[ie("div",fe,[e(u,{type:"primary",onClick:M},{default:l(()=>[m("确 定")]),_:1}),e(u,{onClick:T},{default:l(()=>[m("取 消")]),_:1})])]),default:l(()=>[e(B,{ref:"operatorRef",model:t(i),rules:t(I),"label-width":"100px"},{default:l(()=>[e(y,{label:"公司名称",prop:"companyName"},{default:l(()=>[e(n,{modelValue:t(i).companyName,"onUpdate:modelValue":a[6]||(a[6]=o=>t(i).companyName=o),placeholder:"请输入运营商公司名称"},null,8,["modelValue"])]),_:1}),e(y,{label:"主要联系人",prop:"primaryContactName"},{default:l(()=>[e(n,{modelValue:t(i).primaryContactName,"onUpdate:modelValue":a[7]||(a[7]=o=>t(i).primaryContactName=o),placeholder:"请输入主要联系人姓名"},null,8,["modelValue"])]),_:1}),e(y,{label:"联系手机号",prop:"primaryContactPhone"},{default:l(()=>[e(n,{modelValue:t(i).primaryContactPhone,"onUpdate:modelValue":a[8]||(a[8]=o=>t(i).primaryContactPhone=o),placeholder:"请输入主要联系人手机号",maxlength:"11","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(y,{label:"备注",prop:"remark"},{default:l(()=>[e(n,{modelValue:t(i).remark,"onUpdate:modelValue":a[9]||(a[9]=o=>t(i).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),be=ee(ge,[["__scopeId","data-v-6cbf3010"]]);export{be as default};
