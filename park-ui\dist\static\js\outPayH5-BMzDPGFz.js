import{s as q,u as V,g as M,r as m,p as A,d as N,c as D,h as e,t as c,e as p,f,q as $,o as B}from"./index-kDFdMZgN.js";function S(v){return q({url:"/wx/parking/order/channelPayQuery",method:"post",data:v})}function R(v){return q({url:"/wx/parking/order/paymentTemporaryAlipay",method:"post",data:v})}const U={class:"home"},j={class:"home_bg"},Q={class:"cell"},E={class:"scan_cell"},H=e("div",{class:"cell_header"},[e("div",{class:"scan_cell_title"},"临停缴费"),e("div",{class:"scan_cell_img"})],-1),z={class:"cell_input"},F={class:"cell_title"},G={class:"cell_item"},J=e("div",{class:""},"停车地点",-1),K={class:""},L={class:"cell_item"},O=e("div",{class:""},"入场时间",-1),W={class:""},X={class:"cell_item"},Y=e("div",{class:""},"停车时长",-1),Z={class:""},ee={class:"cell_item"},ae=e("div",{class:""},"应缴金额",-1),se={class:""},oe=e("div",{class:"cell_input_title"},"选择支付方式",-1),te={class:"cell_input_radio"},ne={__name:"outPayH5",setup(v){const t=V(),{proxy:i}=M(),r=m(null),u=m("1"),l=m(!1),o=m("");function b(a){u.value=a}A(()=>{if(t.query.gateNo&&t.query.warehouseId){let a={gateNo:t.query.gateNo,warehouseId:t.query.warehouseId};console.log(a),S(a).then(s=>{r.value=s.data||{}})}});function x(){return l.value?o.value?o.value:u.value==="1"?"正在跳转微信...":"正在跳转支付宝...":"确认支付"}function I(){var a;if(!r.value){i.$message.error("当前订单不存在~");return}if(!((a=r.value)!=null&&a.paymentAmount)){i.$message.error("当前应缴金额为0~");return}l.value||(l.value=!0,o.value="",u.value==="1"?(o.value="正在跳转微信...",k()):(o.value="正在跳转支付宝...",C()))}function k(){try{i.$message.warning("体验版暂不支持微信支付，请使用支付宝支付");return}catch(a){console.error("跳转微信小程序失败:",a),i.$message.error("跳转微信小程序失败，请确保已安装微信"),l.value=!1,o.value=""}}function C(){const a={gateNo:t.query.gateNo,warehouseId:t.query.warehouseId};R(a).then(s=>{s.data?(console.log("跳转支付宝支付:",s.data),location.href=s.data,setTimeout(()=>{l.value=!1,o.value=""},5e3)):(i.$message.error("获取支付宝支付链接失败"),l.value=!1,o.value="")}).catch(s=>{console.error("支付宝支付失败:",s),i.$message.error("支付宝支付失败，请重试"),l.value=!1,o.value=""})}function T(a){if(!a||a<=0)return"--";const s=Math.floor(a),d=Math.floor(s/(24*60)),h=Math.floor(s%(24*60)/60),_=s%60;let n="";return d>0&&(n+=`${d}天`),h>0&&(n+=`${h}小时`),_>0&&(n+=`${_}分钟`),n||(n="0分钟"),n}return(a,s)=>{var _,n,g,y,w;const d=N("el-radio"),h=N("el-radio-group");return B(),D("div",U,[e("div",j,[e("div",Q,[e("div",E,[H,e("div",z,[e("div",F,c(((_=r.value)==null?void 0:_.plateNo)||"--"),1),e("div",G,[J,e("div",K,c(((n=r.value)==null?void 0:n.warehouseName)||"--"),1)]),e("div",L,[O,e("div",W,c(((g=r.value)==null?void 0:g.beginParkingTime)||"--"),1)]),e("div",X,[Y,e("div",Z,c(T((y=r.value)==null?void 0:y.parkingDuration)||"--"),1)]),e("div",ee,[ae,e("div",se,c(((w=r.value)==null?void 0:w.paymentAmount)||"--"),1)]),oe,e("div",te,[p(h,{modelValue:u.value,"onUpdate:modelValue":s[0]||(s[0]=P=>u.value=P),onChange:b},{default:f(()=>[p(d,{label:"1",border:""},{default:f(()=>[$("微信")]),_:1}),p(d,{label:"2",border:""},{default:f(()=>[$("支付宝")]),_:1})]),_:1},8,["modelValue"])])]),e("div",{class:"submit-btn",onClick:I},c(x()),1)])])])])}}};export{ne as default};
