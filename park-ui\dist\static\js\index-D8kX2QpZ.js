import{s as B,_ as Pe,M as Ce,g as xe,r as _,A as De,R as Se,p as Ue,d as m,O as ue,c as d,o as u,P as U,e,Q as Be,f as l,l as Ee,k as t,J as F,K as Q,i as h,q as p,h as w,t as r,Z as E,j as z,y as Re,z as qe}from"./index-kDFdMZgN.js";import{o as ze}from"./warehouse-D0UVqmLR.js";import{C as ie}from"./index-D3uM1NSj.js";function Ye(c){return B({url:"/system/gateParkingInfo/list",method:"get",params:c})}function $e(c){return B({url:"/system/gateParkingInfo/"+c,method:"get"})}function Me(c){return B({url:"/system/gateParkingInfo",method:"post",data:c})}function Oe(c){return B({url:"/system/gateParkingInfo",method:"put",data:c})}function Ge(c){return B({url:"/system/gateParkingInfo/"+c,method:"delete"})}function Fe(){return B({url:"/system/gateParkingInfo/carTypeOptions",method:"get"})}const j=c=>(Re("data-v-9916d5f6"),c=c(),qe(),c),Qe={class:"app-container"},je={key:0},Ke={key:1},Le={key:0},Ae={key:1},Je={key:0,class:"text-red-500 font-medium"},We={key:1},Ze={key:0},He={key:1},Xe={key:0,class:"view-container"},el={key:0},ll={key:1},al={key:1},tl={key:1},nl={key:0,style:{color:"#f56c6c","font-weight":"bold"}},ol={key:1},ul={key:0},il={key:1},sl={class:"image-section"},dl=j(()=>w("h3",{style:{"margin-bottom":"15px",color:"#303133"}},"车辆照片",-1)),rl={class:"image-container"},pl={key:0,class:"image-item"},ml=j(()=>w("h4",null,"入场照片",-1)),cl={key:1,class:"image-item",style:{"margin-top":"20px"}},fl=j(()=>w("h4",null,"出场照片",-1)),gl={key:2,class:"no-image"},_l={class:"dialog-footer"},bl=Ce({name:"GateParkingInfo"}),yl=Object.assign(bl,{components:{CustomPagination:ie}},{setup(c){const{proxy:T}=xe(),K=_([]),P=_(!1),Y=_(!0),$=_(!0),M=_([]),se=_(!0),L=_(!0),A=_(0),J=_(""),C=_(""),x=_(""),O=_([]),W=_([]),b=_(!1),de=De({form:{},queryParams:{pageNum:1,pageSize:10,plateNum:null,parkingId:null,carType:null,status:null,inBeginTime:null,inEndTime:null,outBeginTime:null,outEndTime:null},rules:{plateNum:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],carType:[{required:!0,message:"车辆类型不能为空",trigger:"change"}],parkingId:[{required:!0,message:"场库不能为空",trigger:"change"}]}}),{queryParams:s,form:n,rules:re}=Se(de);function N(){Y.value=!0,s.value.params={},C.value?(s.value.inBeginTime=C.value+" 00:00:00",s.value.inEndTime=C.value+" 23:59:59"):(s.value.inBeginTime=null,s.value.inEndTime=null),x.value?(s.value.outBeginTime=x.value+" 00:00:00",s.value.outEndTime=x.value+" 23:59:59"):(s.value.outBeginTime=null,s.value.outEndTime=null),Ye(s.value).then(i=>{K.value=i.rows,A.value=i.total,Y.value=!1})}function G(){s.value.pageNum=1,N()}function pe(){C.value="",x.value="",T.resetForm("queryRef"),Object.assign(s.value,{pageNum:1,pageSize:10,plateNum:null,parkingId:null,carType:null,status:null,inBeginTime:null,inEndTime:null,outBeginTime:null,outEndTime:null}),G()}function me(i){M.value=i.map(o=>o.id),se.value=i.length!=1,L.value=!i.length}function ce(i){H(),b.value=!0;const o=i.id||M.value[0];$e(o).then(D=>{n.value=D.data,P.value=!0,J.value="查看车辆出入场记录"})}function fe(){T.$refs.gateParkingInfoRef.validate(i=>{i&&(n.value.id!=null?Oe(n.value).then(()=>{T.$modal.msgSuccess("修改成功"),P.value=!1,N()}):Me(n.value).then(()=>{T.$modal.msgSuccess("新增成功"),P.value=!1,N()}))})}function Z(i){const o=i.id||M.value;T.$modal.confirm('是否确认删除车辆出入场记录编号为"'+o+'"的数据项？').then(function(){return Ge(o)}).then(()=>{N(),T.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ge(){T.download("system/gateParkingInfo/export",{...s.value},`车辆出入场记录_${new Date().getTime()}.xlsx`)}function _e(){P.value=!1,H()}function H(){n.value={id:null,parkingId:null,plateNum:null,carType:null,inTime:null,inChannelId:null,inChannelName:null,inPic:null,outTime:null,outChannelId:null,outChannelName:null,outPic:null,money:null,payType:null,status:0},T.resetForm("gateParkingInfoRef")}function X(i){return i?i.length===8?"success":"primary":""}function ee(i){return i?i.length===8?"#d4edda":"#cce7ff":"#909399"}function le(i){if(!i||i==="null"||i==="undefined")return"--";switch(String(i)){case"1":return"支付宝";case"2":return"微信";case"3":return"其他";default:return"--"}}function ae(i){if(i==null)return"--";switch(Number(i)){case 0:return"在场";case 1:return"出场";default:return"--"}}function be(i){i===0&&(n.value.payType=null)}async function ye(){try{const i=await ze();O.value=i.data||[];const o=await Fe();W.value=o.data||[]}catch(i){console.error("初始化数据失败:",i)}}return Ue(()=>{N(),ye()}),(i,o)=>{const D=m("el-input"),f=m("el-form-item"),k=m("el-option"),S=m("el-select"),te=m("el-date-picker"),V=m("el-button"),ne=m("el-form"),y=m("el-col"),he=m("right-toolbar"),I=m("el-row"),v=m("el-table-column"),R=m("el-tag"),ve=m("el-table"),g=m("el-descriptions-item"),ke=m("el-descriptions"),oe=m("el-image"),we=m("el-empty"),Te=m("el-input-number"),Ve=m("el-dialog"),q=ue("hasPermi"),Ne=ue("loading");return u(),d("div",Qe,[U(e(ne,{model:t(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(f,{label:"车牌号",prop:"plateNum"},{default:l(()=>[e(D,{modelValue:t(s).plateNum,"onUpdate:modelValue":o[0]||(o[0]=a=>t(s).plateNum=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:Ee(G,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"场库名称",prop:"parkingId"},{default:l(()=>[e(S,{modelValue:t(s).parkingId,"onUpdate:modelValue":o[1]||(o[1]=a=>t(s).parkingId=a),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:l(()=>[(u(!0),d(F,null,Q(O.value,a=>(u(),h(k,{key:a.id,label:a.warehouseName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"状态",prop:"status"},{default:l(()=>[e(S,{modelValue:t(s).status,"onUpdate:modelValue":o[2]||(o[2]=a=>t(s).status=a),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:l(()=>[e(k,{label:"在场",value:"0"}),e(k,{label:"已出场",value:"1"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"入场时间",prop:"inDate"},{default:l(()=>[e(te,{modelValue:C.value,"onUpdate:modelValue":o[3]||(o[3]=a=>C.value=a),"value-format":"YYYY-MM-DD",type:"date",placeholder:"选择入场日期",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(f,{label:"出场时间",prop:"outDate"},{default:l(()=>[e(te,{modelValue:x.value,"onUpdate:modelValue":o[4]||(o[4]=a=>x.value=a),"value-format":"YYYY-MM-DD",type:"date",placeholder:"选择出场日期",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(f,null,{default:l(()=>[e(V,{type:"primary",icon:"Search",onClick:G},{default:l(()=>[p("搜索")]),_:1}),e(V,{icon:"Refresh",onClick:pe},{default:l(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Be,$.value]]),e(I,{gutter:10,class:"mb8"},{default:l(()=>[e(y,{span:1.5},{default:l(()=>[U((u(),h(V,{type:"danger",plain:"",icon:"Delete",disabled:L.value,onClick:Z},{default:l(()=>[p("删除")]),_:1},8,["disabled"])),[[q,["order:gateParkingInfo:remove"]]])]),_:1}),e(y,{span:1.5},{default:l(()=>[U((u(),h(V,{type:"warning",plain:"",icon:"Download",onClick:ge},{default:l(()=>[p("导出")]),_:1})),[[q,["order:gateParkingInfo:export"]]])]),_:1}),e(he,{showSearch:$.value,"onUpdate:showSearch":o[5]||(o[5]=a=>$.value=a),onQueryTable:N},null,8,["showSearch"])]),_:1}),U((u(),h(ve,{data:K.value,onSelectionChange:me},{default:l(()=>[e(v,{type:"selection",width:"55",align:"center"}),e(v,{label:"场库名称",align:"center",prop:"parkingName","min-width":"120"},{default:l(a=>[w("span",null,r(a.row.parkingName||"--"),1)]),_:1}),e(v,{label:"车辆类型",align:"center",prop:"carType",width:"120"},{default:l(a=>[w("span",null,r(a.row.carType||"--"),1)]),_:1}),e(v,{label:"车牌号",align:"center",prop:"plateNum",width:"120"},{default:l(a=>[e(R,{type:X(a.row.plateNum),color:ee(a.row.plateNum),effect:"plain",class:"plate-tag"},{default:l(()=>[p(r(a.row.plateNum),1)]),_:2},1032,["type","color"])]),_:1}),e(v,{label:"状态",align:"center",prop:"status",width:"80"},{default:l(a=>[e(R,{type:a.row.status===0?"warning":"success"},{default:l(()=>[p(r(ae(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"入场时间",align:"center",prop:"inDateTime","min-width":"160"},{default:l(a=>[a.row.inDateTime?(u(),d("span",je,r(t(E)(a.row.inDateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),d("span",Ke,"--"))]),_:1}),e(v,{label:"出场时间",align:"center",prop:"outDateTime","min-width":"160"},{default:l(a=>[a.row.outDateTime?(u(),d("span",Le,r(t(E)(a.row.outDateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),d("span",Ae,"--"))]),_:1}),e(v,{label:"停车时长",align:"center",prop:"parkingDurationText","min-width":"120"},{default:l(a=>[w("span",null,r(a.row.parkingDurationText||"--"),1)]),_:1}),e(v,{label:"停车费用",align:"center",prop:"money",width:"100"},{default:l(a=>[a.row.money!==null&&a.row.money!==void 0?(u(),d("span",Je,"¥"+r(a.row.money),1)):(u(),d("span",We,"--"))]),_:1}),e(v,{label:"支付类型",align:"center",prop:"payType","min-width":"100"},{default:l(a=>[a.row.status===0||!a.row.payType?(u(),d("span",Ze,"--")):(u(),d("span",He,r(le(a.row.payType)),1))]),_:1}),e(v,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:l(a=>[U((u(),h(V,{link:"",type:"primary",icon:"View",onClick:Ie=>ce(a.row)},{default:l(()=>[p("查看")]),_:2},1032,["onClick"])),[[q,["order:gateParkingInfo:query"]]]),U((u(),h(V,{link:"",type:"primary",icon:"Delete",onClick:Ie=>Z(a.row)},{default:l(()=>[p("删除")]),_:2},1032,["onClick"])),[[q,["order:gateParkingInfo:remove"]]])]),_:1})]),_:1},8,["data"])),[[Ne,Y.value]]),e(ie,{total:A.value,"current-page":t(s).pageNum,"onUpdate:currentPage":o[6]||(o[6]=a=>t(s).pageNum=a),"page-size":t(s).pageSize,"onUpdate:pageSize":o[7]||(o[7]=a=>t(s).pageSize=a),onPagination:N},null,8,["total","current-page","page-size"]),e(Ve,{title:J.value,modelValue:P.value,"onUpdate:modelValue":o[16]||(o[16]=a=>P.value=a),width:b.value?"1200px":"600px","append-to-body":""},{footer:l(()=>[w("div",_l,[e(V,{onClick:_e},{default:l(()=>[p(r(b.value?"关 闭":"取 消"),1)]),_:1}),b.value?z("",!0):(u(),h(V,{key:0,type:"primary",onClick:fe},{default:l(()=>[p("确 定")]),_:1}))])]),default:l(()=>[b.value?(u(),d("div",Xe,[e(I,{gutter:20},{default:l(()=>[e(y,{span:14},{default:l(()=>[e(ke,{title:"车辆出入场记录详情",column:2,border:""},{default:l(()=>[e(g,{label:"场库名称","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).parkingName||"--"),1)]),_:1}),e(g,{label:"更新时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(n).lastUpdate?(u(),d("span",el,r(t(E)(t(n).lastUpdate,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),d("span",ll,"--"))]),_:1}),e(g,{label:"车牌号","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(n).plateNum?(u(),h(R,{key:0,type:X(t(n).plateNum),color:ee(t(n).plateNum),effect:"plain",class:"plate-tag"},{default:l(()=>[p(r(t(n).plateNum),1)]),_:1},8,["type","color"])):(u(),d("span",al,"--"))]),_:1}),e(g,{label:"状态","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(n).status!==null&&t(n).status!==void 0?(u(),h(R,{key:0,type:t(n).status===0?"warning":"success"},{default:l(()=>[p(r(ae(t(n).status)),1)]),_:1},8,["type"])):(u(),d("span",tl,"--"))]),_:1}),e(g,{label:"车辆类型","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).carType||"--"),1)]),_:1}),e(g,{label:"停车时长","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).parkingDurationText||"--"),1)]),_:1}),e(g,{label:"入场ID","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).inChannelId||"--"),1)]),_:1}),e(g,{label:"出场ID","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).outChannelId||"--"),1)]),_:1}),e(g,{label:"入场通道","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).inChannelName||"--"),1)]),_:1}),e(g,{label:"出场通道","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).outChannelName||"--"),1)]),_:1}),e(g,{label:"入场时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).inDateTime?t(E)(t(n).inDateTime,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1}),e(g,{label:"出场时间","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[p(r(t(n).outDateTime?t(E)(t(n).outDateTime,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1}),e(g,{label:"停车费用","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(n).money!==null&&t(n).money!==void 0?(u(),d("span",nl," ¥"+r(t(n).money),1)):(u(),d("span",ol,"--"))]),_:1}),e(g,{label:"支付类型","label-align":"right","label-class-name":"desc-label"},{default:l(()=>[t(n).status===0||!t(n).payType?(u(),d("span",ul,"--")):(u(),d("span",il,r(le(t(n).payType)),1))]),_:1})]),_:1})]),_:1}),e(y,{span:10},{default:l(()=>[w("div",sl,[dl,w("div",rl,[t(n).inPic?(u(),d("div",pl,[ml,e(oe,{src:t(n).inPic,fit:"cover",style:{width:"100%",height:"200px","border-radius":"4px"},"preview-src-list":[t(n).inPic],"preview-teleported":""},null,8,["src","preview-src-list"])])):z("",!0),t(n).outPic?(u(),d("div",cl,[fl,e(oe,{src:t(n).outPic,fit:"cover",style:{width:"100%",height:"200px","border-radius":"4px"},"preview-src-list":[t(n).outPic],"preview-teleported":""},null,8,["src","preview-src-list"])])):z("",!0),!t(n).inPic&&!t(n).outPic?(u(),d("div",gl,[e(we,{description:"暂无照片"})])):z("",!0)])])]),_:1})]),_:1})])):(u(),h(ne,{key:1,ref:"gateParkingInfoRef",model:t(n),rules:t(re),"label-width":"80px"},{default:l(()=>[e(I,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(f,{label:"车牌号",prop:"plateNum"},{default:l(()=>[e(D,{modelValue:t(n).plateNum,"onUpdate:modelValue":o[8]||(o[8]=a=>t(n).plateNum=a),placeholder:"请输入车牌号",disabled:b.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(f,{label:"车辆类型",prop:"carType"},{default:l(()=>[e(S,{modelValue:t(n).carType,"onUpdate:modelValue":o[9]||(o[9]=a=>t(n).carType=a),placeholder:"请选择车辆类型",disabled:b.value},{default:l(()=>[(u(!0),d(F,null,Q(W.value,a=>(u(),h(k,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,null,{default:l(()=>[e(y,{span:24},{default:l(()=>[e(f,{label:"场库",prop:"parkingId"},{default:l(()=>[e(S,{modelValue:t(n).parkingId,"onUpdate:modelValue":o[10]||(o[10]=a=>t(n).parkingId=a),placeholder:"请选择场库",disabled:b.value},{default:l(()=>[(u(!0),d(F,null,Q(O.value,a=>(u(),h(k,{key:a.id,label:a.warehouseName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(f,{label:"入场通道",prop:"inChannelName"},{default:l(()=>[e(D,{modelValue:t(n).inChannelName,"onUpdate:modelValue":o[11]||(o[11]=a=>t(n).inChannelName=a),placeholder:"请输入入场通道",disabled:b.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(f,{label:"出场通道",prop:"outChannelName"},{default:l(()=>[e(D,{modelValue:t(n).outChannelName,"onUpdate:modelValue":o[12]||(o[12]=a=>t(n).outChannelName=a),placeholder:"请输入出场通道",disabled:b.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(f,{label:"停车费用",prop:"money"},{default:l(()=>[e(Te,{modelValue:t(n).money,"onUpdate:modelValue":o[13]||(o[13]=a=>t(n).money=a),precision:2,min:0,disabled:b.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(f,{label:"支付类型",prop:"payType"},{default:l(()=>[e(S,{modelValue:t(n).payType,"onUpdate:modelValue":o[14]||(o[14]=a=>t(n).payType=a),placeholder:"请选择支付类型",disabled:b.value||t(n).status===0},{default:l(()=>[e(k,{label:"支付宝",value:1}),e(k,{label:"微信",value:2}),e(k,{label:"其他",value:99})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,null,{default:l(()=>[e(y,{span:24},{default:l(()=>[e(f,{label:"状态",prop:"status"},{default:l(()=>[e(S,{modelValue:t(n).status,"onUpdate:modelValue":o[15]||(o[15]=a=>t(n).status=a),placeholder:"请选择状态",disabled:b.value,onChange:be},{default:l(()=>[e(k,{label:"在场",value:0}),e(k,{label:"已出场",value:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]))]),_:1},8,["title","modelValue","width"])])}}}),wl=Pe(yl,[["__scopeId","data-v-9916d5f6"]]);export{wl as default};
